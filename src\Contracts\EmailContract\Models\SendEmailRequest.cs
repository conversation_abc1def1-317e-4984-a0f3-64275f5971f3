using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace EmailContract.Models;

/// <summary>
/// Represents a request to send an email notification through the email service.
/// </summary>
/// <remarks>
/// This contract defines the essential information required to send an email,
/// including recipient details, subject, and message content. All fields are required
/// to ensure successful email delivery.
/// </remarks>
public sealed record SendEmailRequest
{
    /// <summary>
    /// Gets the email address of the recipient.
    /// </summary>
    /// <example><EMAIL></example>
    [JsonPropertyName("receptorMail")]
    [Required(ErrorMessage = "Recipient email address is required")]
    [EmailAddress(ErrorMessage = "Invalid email address format")]
    [StringLength(320, ErrorMessage = "Email address cannot exceed 320 characters")]
    public required string ReceptorMail { get; init; }

    /// <summary>
    /// Gets the display name of the recipient.
    /// </summary>
    /// <example><PERSON></example>
    [JsonPropertyName("receptorName")]
    [Required(ErrorMessage = "Recipient name is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Recipient name must be between 1 and 100 characters")]
    public required string ReceptorName { get; init; }

    /// <summary>
    /// Gets the subject line of the email.
    /// </summary>
    /// <example>Welcome to our service!</example>
    [JsonPropertyName("subject")]
    [Required(ErrorMessage = "Email subject is required")]
    [StringLength(200, MinimumLength = 1, ErrorMessage = "Subject must be between 1 and 200 characters")]
    public required string Subject { get; init; }

    /// <summary>
    /// Gets the body content of the email.
    /// </summary>
    /// <remarks>
    /// Supports both plain text and HTML content. The email service will handle
    /// appropriate formatting based on the content type.
    /// </remarks>
    /// <example>Thank you for joining our service. We're excited to have you!</example>
    [JsonPropertyName("body")]
    [Required(ErrorMessage = "Email body is required")]
    [StringLength(50000, MinimumLength = 1, ErrorMessage = "Body must be between 1 and 50,000 characters")]
    public required string Body { get; init; }
}