using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace NotificationContract.Models;

/// <summary>
/// Represents a comprehensive email message request for gateway operations.
/// </summary>
/// <remarks>
/// This model supports advanced email features including HTML content, multiple recipients,
/// and file attachments. It's designed for use with email gateway services that require
/// more detailed configuration than the basic email contract.
/// </remarks>
public class EmailMessageRequest
{
    /// <summary>
    /// Gets or sets the primary recipient email address.
    /// </summary>
    /// <example><EMAIL></example>
    [JsonPropertyName("to")]
    [Required(ErrorMessage = "Recipient email address is required")]
    [EmailAddress(ErrorMessage = "Invalid email address format")]
    [StringLength(320, ErrorMessage = "Email address cannot exceed 320 characters")]
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the subject line of the email.
    /// </summary>
    /// <example>Important Update</example>
    [JsonPropertyName("subject")]
    [Required(ErrorMessage = "Email subject is required")]
    [StringLength(200, MinimumLength = 1, ErrorMessage = "Subject must be between 1 and 200 characters")]
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the body content of the email.
    /// </summary>
    /// <example>This is the email content.</example>
    [JsonPropertyName("body")]
    [Required(ErrorMessage = "Email body is required")]
    [StringLength(100000, MinimumLength = 1, ErrorMessage = "Body must be between 1 and 100,000 characters")]
    public string Body { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets a value indicating whether the body content is HTML formatted.
    /// </summary>
    [JsonPropertyName("isHtml")]
    public bool IsHtml { get; set; } = true;

    /// <summary>
    /// Gets or sets the sender email address. If not specified, uses the default sender.
    /// </summary>
    /// <example><EMAIL></example>
    [JsonPropertyName("from")]
    [EmailAddress(ErrorMessage = "Invalid sender email address format")]
    [StringLength(320, ErrorMessage = "Sender email address cannot exceed 320 characters")]
    public string? From { get; set; }

    /// <summary>
    /// Gets or sets the list of carbon copy (CC) recipient email addresses.
    /// </summary>
    [JsonPropertyName("cc")]
    public List<string>? Cc { get; set; }

    /// <summary>
    /// Gets or sets the list of blind carbon copy (BCC) recipient email addresses.
    /// </summary>
    [JsonPropertyName("bcc")]
    public List<string>? Bcc { get; set; }

    /// <summary>
    /// Gets or sets the list of file attachments to include with the email.
    /// </summary>
    [JsonPropertyName("attachments")]
    public List<EmailAttachment>? Attachments { get; set; }
}

/// <summary>
/// Represents an email attachment with file content and metadata.
/// </summary>
/// <remarks>
/// Supports various file types with proper content type specification.
/// Content is stored as base64-encoded byte array for JSON serialization.
/// </remarks>
public class EmailAttachment
{
    /// <summary>
    /// Gets or sets the name of the attachment file.
    /// </summary>
    /// <example>document.pdf</example>
    [JsonPropertyName("fileName")]
    [Required(ErrorMessage = "Attachment file name is required")]
    [StringLength(255, MinimumLength = 1, ErrorMessage = "File name must be between 1 and 255 characters")]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the binary content of the attachment.
    /// </summary>
    /// <remarks>
    /// Content should be provided as a byte array. For JSON serialization,
    /// this will be automatically converted to/from base64 encoding.
    /// </remarks>
    [JsonPropertyName("content")]
    [Required(ErrorMessage = "Attachment content is required")]
    [MaxLength(10485760, ErrorMessage = "Attachment cannot exceed 10MB")]
    public byte[] Content { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// Gets or sets the MIME content type of the attachment.
    /// </summary>
    /// <example>application/pdf</example>
    [JsonPropertyName("contentType")]
    [Required(ErrorMessage = "Content type is required")]
    [StringLength(100, ErrorMessage = "Content type cannot exceed 100 characters")]
    public string ContentType { get; set; } = string.Empty;
}

/// <summary>
/// Represents a request to send multiple emails in a single batch operation.
/// </summary>
/// <remarks>
/// Bulk operations are more efficient for sending multiple emails and provide
/// better error handling and status tracking for each individual message.
/// </remarks>
public class BulkEmailRequest
{
    /// <summary>
    /// Gets or sets the list of email messages to send.
    /// </summary>
    [JsonPropertyName("messages")]
    [Required(ErrorMessage = "At least one email message is required")]
    [MinLength(1, ErrorMessage = "At least one email message must be provided")]
    [MaxLength(1000, ErrorMessage = "Cannot send more than 1000 emails in a single batch")]
    public List<EmailMessageRequest> Messages { get; set; } = [];
}

/// <summary>
/// Represents the response from a single email send operation within a gateway.
/// </summary>
/// <remarks>
/// This response provides detailed information about the email sending attempt,
/// including success status and any error details.
/// </remarks>
public class EmailResponse
{
    /// <summary>
    /// Gets or sets a value indicating whether the email was successfully sent.
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the unique identifier for the sent email message.
    /// </summary>
    /// <example>email_**********abcdef</example>
    [JsonPropertyName("messageId")]
    [StringLength(100, ErrorMessage = "Message ID cannot exceed 100 characters")]
    public string? MessageId { get; set; }

    /// <summary>
    /// Gets or sets the error message if the email sending failed.
    /// </summary>
    /// <example>Invalid email address format</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="EmailResponse"/> class.
    /// </summary>
    /// <param name="isSuccess">Whether the operation was successful</param>
    /// <param name="errorMessage">Error message if the operation failed</param>
    /// <param name="messageId">Message identifier if the operation succeeded</param>
    public EmailResponse(bool isSuccess, string? errorMessage = null, string? messageId = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        MessageId = messageId;
    }
}

/// <summary>
/// Represents the response from a bulk email send operation.
/// </summary>
/// <remarks>
/// Contains the overall operation status and individual results for each email
/// in the batch, allowing for detailed error tracking and partial success scenarios.
/// </remarks>
public class BulkEmailResponse
{
    /// <summary>
    /// Gets or sets a value indicating whether the bulk operation was successful.
    /// </summary>
    /// <remarks>
    /// This indicates whether the bulk operation itself succeeded, not necessarily
    /// that all individual emails were sent successfully. Check Results for individual status.
    /// </remarks>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the list of individual email send results.
    /// </summary>
    [JsonPropertyName("results")]
    public List<EmailResponse> Results { get; set; } = [];

    /// <summary>
    /// Gets or sets the error message if the bulk operation failed.
    /// </summary>
    /// <example>Bulk operation failed due to rate limiting</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="BulkEmailResponse"/> class.
    /// </summary>
    /// <param name="isSuccess">Whether the bulk operation was successful</param>
    /// <param name="errorMessage">Error message if the bulk operation failed</param>
    public BulkEmailResponse(bool isSuccess, string? errorMessage = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
    }
}

/// <summary>
/// Represents an SMS message request for gateway operations.
/// </summary>
/// <remarks>
/// This model provides the essential information needed to send an SMS message
/// through various SMS gateway providers.
/// </remarks>
public class SmsMessageRequest
{
    /// <summary>
    /// Gets or sets the recipient phone number in international format.
    /// </summary>
    /// <example>+**********</example>
    [JsonPropertyName("phoneNumber")]
    [Required(ErrorMessage = "Phone number is required")]
    [Phone(ErrorMessage = "Invalid phone number format")]
    [StringLength(20, MinimumLength = 10, ErrorMessage = "Phone number must be between 10 and 20 characters")]
    public string PhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the SMS message content.
    /// </summary>
    /// <example>Your verification code is 123456</example>
    [JsonPropertyName("message")]
    [Required(ErrorMessage = "SMS message is required")]
    [StringLength(1600, MinimumLength = 1, ErrorMessage = "Message must be between 1 and 1600 characters")]
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Represents a request to send multiple SMS messages in a single batch operation.
/// </summary>
/// <remarks>
/// Bulk SMS operations are more efficient for sending multiple messages and provide
/// better error handling and status tracking for each individual message.
/// </remarks>
public class BulkSmsRequest
{
    /// <summary>
    /// Gets or sets the list of SMS messages to send.
    /// </summary>
    [JsonPropertyName("messages")]
    [Required(ErrorMessage = "At least one SMS message is required")]
    [MinLength(1, ErrorMessage = "At least one SMS message must be provided")]
    [MaxLength(1000, ErrorMessage = "Cannot send more than 1000 SMS messages in a single batch")]
    public List<SmsMessageRequest> Messages { get; set; } = [];
}

/// <summary>
/// Represents the response from a single SMS send operation within a gateway.
/// </summary>
/// <remarks>
/// This response provides detailed information about the SMS sending attempt,
/// including success status and any error details.
/// </remarks>
public class SmsResponse
{
    /// <summary>
    /// Gets or sets a value indicating whether the SMS was successfully sent.
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the unique identifier for the sent SMS message.
    /// </summary>
    /// <example>sms_**********abcdef</example>
    [JsonPropertyName("messageId")]
    [StringLength(100, ErrorMessage = "Message ID cannot exceed 100 characters")]
    public string? MessageId { get; set; }

    /// <summary>
    /// Gets or sets the error message if the SMS sending failed.
    /// </summary>
    /// <example>Invalid phone number format</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="SmsResponse"/> class.
    /// </summary>
    /// <param name="isSuccess">Whether the operation was successful</param>
    /// <param name="errorMessage">Error message if the operation failed</param>
    /// <param name="messageId">Message identifier if the operation succeeded</param>
    public SmsResponse(bool isSuccess, string? errorMessage = null, string? messageId = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        MessageId = messageId;
    }
}

/// <summary>
/// Represents the response from a bulk SMS send operation.
/// </summary>
/// <remarks>
/// Contains the overall operation status and individual results for each SMS
/// in the batch, allowing for detailed error tracking and partial success scenarios.
/// </remarks>
public class BulkSmsResponse
{
    /// <summary>
    /// Gets or sets a value indicating whether the bulk operation was successful.
    /// </summary>
    /// <remarks>
    /// This indicates whether the bulk operation itself succeeded, not necessarily
    /// that all individual SMS messages were sent successfully. Check Results for individual status.
    /// </remarks>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the list of individual SMS send results.
    /// </summary>
    [JsonPropertyName("results")]
    public List<SmsResponse> Results { get; set; } = [];

    /// <summary>
    /// Gets or sets the error message if the bulk operation failed.
    /// </summary>
    /// <example>Bulk operation failed due to rate limiting</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="BulkSmsResponse"/> class.
    /// </summary>
    /// <param name="isSuccess">Whether the bulk operation was successful</param>
    /// <param name="errorMessage">Error message if the bulk operation failed</param>
    public BulkSmsResponse(bool isSuccess, string? errorMessage = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
    }
}

/// <summary>
/// Represents a push notification message request for gateway operations.
/// </summary>
/// <remarks>
/// This model provides the essential information needed to send a push notification
/// through various push notification gateway providers like Firebase.
/// </remarks>
public class PushMessageRequest
{
    /// <summary>
    /// Gets or sets the unique device token for the target device.
    /// </summary>
    /// <example>dGVzdF90b2tlbl8xMjM0NTY3ODkw</example>
    [JsonPropertyName("deviceToken")]
    [Required(ErrorMessage = "Device token is required")]
    [StringLength(500, MinimumLength = 10, ErrorMessage = "Device token must be between 10 and 500 characters")]
    public string DeviceToken { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the title of the push notification.
    /// </summary>
    /// <example>New Message</example>
    [JsonPropertyName("title")]
    [Required(ErrorMessage = "Notification title is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Title must be between 1 and 100 characters")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the body message of the push notification.
    /// </summary>
    /// <example>You have received a new message.</example>
    [JsonPropertyName("body")]
    [Required(ErrorMessage = "Notification body is required")]
    [StringLength(500, MinimumLength = 1, ErrorMessage = "Body must be between 1 and 500 characters")]
    public string Body { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets optional additional data to include with the notification.
    /// </summary>
    [JsonPropertyName("data")]
    public Dictionary<string, string>? Data { get; set; }
}

/// <summary>
/// Represents a request to send multiple push notifications in a single batch operation.
/// </summary>
/// <remarks>
/// Bulk push operations are more efficient for sending multiple notifications and provide
/// better error handling and status tracking for each individual message.
/// </remarks>
public class BulkPushRequest
{
    /// <summary>
    /// Gets or sets the list of push notification messages to send.
    /// </summary>
    [JsonPropertyName("messages")]
    [Required(ErrorMessage = "At least one push notification message is required")]
    [MinLength(1, ErrorMessage = "At least one push notification message must be provided")]
    [MaxLength(1000, ErrorMessage = "Cannot send more than 1000 push notifications in a single batch")]
    public List<PushMessageRequest> Messages { get; set; } = [];
}

/// <summary>
/// Represents the response from a single push notification send operation within a gateway.
/// </summary>
/// <remarks>
/// This response provides detailed information about the push notification sending attempt,
/// including success status and any error details.
/// </remarks>
public class PushResponse
{
    /// <summary>
    /// Gets or sets a value indicating whether the push notification was successfully sent.
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the unique identifier for the sent push notification message.
    /// </summary>
    /// <example>push_**********abcdef</example>
    [JsonPropertyName("messageId")]
    [StringLength(100, ErrorMessage = "Message ID cannot exceed 100 characters")]
    public string? MessageId { get; set; }

    /// <summary>
    /// Gets or sets the error message if the push notification sending failed.
    /// </summary>
    /// <example>Invalid device token</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="PushResponse"/> class.
    /// </summary>
    /// <param name="isSuccess">Whether the operation was successful</param>
    /// <param name="errorMessage">Error message if the operation failed</param>
    /// <param name="messageId">Message identifier if the operation succeeded</param>
    public PushResponse(bool isSuccess, string? errorMessage = null, string? messageId = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        MessageId = messageId;
    }
}

/// <summary>
/// Represents the response from a bulk push notification send operation.
/// </summary>
/// <remarks>
/// Contains the overall operation status and individual results for each push notification
/// in the batch, allowing for detailed error tracking and partial success scenarios.
/// </remarks>
public class BulkPushResponse
{
    /// <summary>
    /// Gets or sets a value indicating whether the bulk operation was successful.
    /// </summary>
    /// <remarks>
    /// This indicates whether the bulk operation itself succeeded, not necessarily
    /// that all individual push notifications were sent successfully. Check Results for individual status.
    /// </remarks>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the list of individual push notification send results.
    /// </summary>
    [JsonPropertyName("results")]
    public List<PushResponse> Results { get; set; } = [];

    /// <summary>
    /// Gets or sets the error message if the bulk operation failed.
    /// </summary>
    /// <example>Bulk operation failed due to rate limiting</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="BulkPushResponse"/> class.
    /// </summary>
    /// <param name="isSuccess">Whether the bulk operation was successful</param>
    /// <param name="errorMessage">Error message if the bulk operation failed</param>
    public BulkPushResponse(bool isSuccess, string? errorMessage = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
    }
}

/// <summary>
/// Represents a generic service operation result.
/// </summary>
/// <remarks>
/// This model provides a standardized way to return operation results across
/// different service operations, including success status, error information, and data.
/// </remarks>
public class ServiceResult
{
    /// <summary>
    /// Gets or sets a value indicating whether the service operation was successful.
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed.
    /// </summary>
    /// <example>Operation failed due to invalid parameters</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets additional data returned by the operation.
    /// </summary>
    [JsonPropertyName("data")]
    public object? Data { get; set; }

    /// <summary>
    /// Gets or sets the number of items purged during cleanup operations.
    /// </summary>
    [JsonPropertyName("purgedCount")]
    [Range(0, int.MaxValue, ErrorMessage = "Purged count must be a non-negative number")]
    public int PurgedCount { get; set; }
}

/// <summary>
/// Represents the status response for a specific message.
/// </summary>
/// <remarks>
/// This model provides detailed status information for tracking message delivery
/// and processing across different notification channels.
/// </remarks>
public class MessageStatusResponse
{
    /// <summary>
    /// Gets or sets a value indicating whether the status request was successful.
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the unique identifier of the message.
    /// </summary>
    /// <example>msg_**********abcdef</example>
    [JsonPropertyName("messageId")]
    [StringLength(100, ErrorMessage = "Message ID cannot exceed 100 characters")]
    public string? MessageId { get; set; }

    /// <summary>
    /// Gets or sets the current status of the message.
    /// </summary>
    /// <example>Delivered</example>
    [JsonPropertyName("status")]
    [StringLength(50, ErrorMessage = "Status cannot exceed 50 characters")]
    public string? Status { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the message was sent.
    /// </summary>
    [JsonPropertyName("sentAt")]
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the message was delivered.
    /// </summary>
    [JsonPropertyName("deliveredAt")]
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Gets or sets the error message if the status request failed.
    /// </summary>
    /// <example>Message not found</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets additional metadata about the message status.
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Represents the response containing message history information.
/// </summary>
/// <remarks>
/// This model provides a paginated list of historical messages with their
/// status information for tracking and auditing purposes.
/// </remarks>
public class MessageHistoryResponse
{
    /// <summary>
    /// Gets or sets a value indicating whether the history request was successful.
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the list of historical message items.
    /// </summary>
    [JsonPropertyName("messages")]
    public List<MessageHistoryItem> Messages { get; set; } = [];

    /// <summary>
    /// Gets or sets the error message if the history request failed.
    /// </summary>
    /// <example>Access denied to message history</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the total count of messages available in the history.
    /// </summary>
    /// <remarks>
    /// This represents the total number of messages, which may be larger than
    /// the number of items returned in the current page.
    /// </remarks>
    [JsonPropertyName("totalCount")]
    [Range(0, int.MaxValue, ErrorMessage = "Total count must be a non-negative number")]
    public int TotalCount { get; set; }
}

/// <summary>
/// Represents a single item in the message history.
/// </summary>
/// <remarks>
/// This model contains the essential information about a historical message,
/// including its content, status, and timing information.
/// </remarks>
public class MessageHistoryItem
{
    /// <summary>
    /// Gets or sets the unique identifier of the message.
    /// </summary>
    /// <example>msg_**********abcdef</example>
    [JsonPropertyName("messageId")]
    [Required(ErrorMessage = "Message ID is required")]
    [StringLength(100, ErrorMessage = "Message ID cannot exceed 100 characters")]
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the content of the message.
    /// </summary>
    /// <example>Your verification code is 123456</example>
    [JsonPropertyName("content")]
    [Required(ErrorMessage = "Message content is required")]
    [StringLength(5000, ErrorMessage = "Content cannot exceed 5000 characters")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the current status of the message.
    /// </summary>
    /// <example>Delivered</example>
    [JsonPropertyName("status")]
    [Required(ErrorMessage = "Message status is required")]
    [StringLength(50, ErrorMessage = "Status cannot exceed 50 characters")]
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the timestamp when the message was sent.
    /// </summary>
    [JsonPropertyName("sentAt")]
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the message was delivered.
    /// </summary>
    [JsonPropertyName("deliveredAt")]
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Gets or sets the error message if the message failed to send.
    /// </summary>
    /// <example>Invalid recipient address</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }
}
