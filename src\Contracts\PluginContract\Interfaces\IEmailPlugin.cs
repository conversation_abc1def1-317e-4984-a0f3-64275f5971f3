using PluginContract.Models;

namespace PluginContract.Interfaces;

/// <summary>
/// Specific interface for email plugins
/// </summary>
public interface IEmailPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendEmailAsync(EmailRequest request, CancellationToken cancellationToken = default);
}
/// <summary>
///  Model for representing a request to send an email
/// </summary>
/// <param name="To"> Email address of the recipient </param>
/// <param name="Subject"> Subject of the email </param>
/// <param name="Body"> Body of the email </param>
/// <param name="From"> Email address of the sender </param>
/// <param name="Cc"> Email addresses to carbon copy </param>
/// <param name="Bcc"> Email addresses to blind carbon copy </param>
/// <param name="Attachments"> Attachments to the email </param>
/// <param name="Metadata"> Metadata of the request </param>
public sealed record EmailRequest(
    string To,
    string Subject,
    string Body,
    string? From = null,
    List<string>? Cc = null,
    List<string>? Bcc = null,
    List<EmailAttachment>? Attachments = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Body, Metadata);
/// <summary>
///  Model for representing an email attachment
/// </summary>
/// <param name="FileName"> Name of the attachment </param>
/// <param name="Content"> Content of the attachment </param>
/// <param name="ContentType"> Content type of the attachment </param>
public sealed record EmailAttachment(
    string FileName,
    byte[] Content,
    string ContentType
);
/// <summary>
///  Model for representing a response to sending an email
/// </summary>
/// <param name="IsSuccess"> Indicates whether the request was successful </param>
/// <param name="MessageId"> Message id of the email </param>
/// <param name="ErrorMessage"> Error message if the request was not successful </param>
/// <param name="ResponseData"> Response data if the request was successful </param>
public sealed record EmailResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
