using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PluginContract.Models;

namespace PluginContract.Interfaces;

/// <summary>
/// Base interface for all notification plugins
/// </summary>
public interface INotificationPlugin
{
    /// <summary>
    /// Plugin information
    /// </summary>
    PluginInfo PluginInfo { get; }
    
    /// <summary>
    /// Configure services for dependency injection
    /// </summary>
    void ConfigureServices(IServiceCollection services, IConfiguration configuration);
    
    /// <summary>
    /// Validate plugin configuration
    /// </summary>
    Task<bool> ValidateConfigurationAsync(IConfiguration configuration);
    
    /// <summary>
    /// Initialize the plugin
    /// </summary>
    Task InitializeAsync(IConfiguration configuration);
    
    /// <summary>
    /// Send notification
    /// </summary>
    Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Health check for the plugin
    /// </summary>
    Task<bool> HealthCheckAsync();
}
