using PluginContract.Models;

namespace PluginContract.Interfaces;

/// <summary>
/// Specific interface for push notification plugins
/// </summary>
public interface IPushNotificationPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendPushNotificationAsync(PushNotificationRequest request, CancellationToken cancellationToken = default);
}
/// <summary>
///  Model for representing a request to send a push notification
/// </summary>
/// <param name="DeviceToken"> Device token of the recipient </param>
/// <param name="Title"> Title of the push notification </param>
/// <param name="Body"> Body of the push notification </param>
/// <param name="Data"> Data of the push notification </param>
public sealed record PushNotificationRequest(
    string DeviceToken,
    string Title,
    string Body,
    Dictionary<string, object>? Data = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Body, Metadata);

/// <summary>
///  Model for representing a response to sending a push notification
/// </summary>
/// <param name="IsSuccess"> Indicates whether the request was successful </param>
/// <param name="MessageId"> Message id of the push notification </param>
/// <param name="ErrorMessage"> Error message if the request was not successful </param>
/// <param name="ResponseData"> Response data if the request was successful </param>
public sealed record PushNotificationResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
