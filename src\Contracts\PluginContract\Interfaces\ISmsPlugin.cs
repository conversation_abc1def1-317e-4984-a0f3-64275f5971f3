using PluginContract.Models;

namespace PluginContract.Interfaces;

/// <summary>
/// Specific interface for SMS plugins
/// </summary>
public interface ISmsPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default);
}
/// <summary>
///  Model for representing a request to send an SMS
/// </summary>
/// <param name="PhoneNumber"> Phone number of the recipient </param>
/// <param name="Message"> Message of the SMS </param>
/// <param name="From"> Phone number of the sender </param>
/// <param name="Metadata"> Metadata of the request </param>
public sealed record SmsRequest(
    string PhoneNumber,
    string Message,
    string? From = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Message, Metadata);
/// <summary>
///  Model for representing a response to sending an SMS
/// </summary>
/// <param name="IsSuccess"> Indicates whether the request was successful </param>
/// <param name="MessageId"> Message id of the SMS </param>
/// <param name="ErrorMessage"> Error message if the request was not successful </param>
/// <param name="ResponseData"> Response data if the request was successful </param>
public sealed record SmsResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
