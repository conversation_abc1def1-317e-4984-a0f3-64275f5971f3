using PluginContract.Enums;

namespace PluginContract.Models;

public sealed record PluginInfo(
    string Name,
    string Version,
    string Description,
    PluginType Type,
    string Author,
    bool IsEnabled = true
);

public sealed record PluginConfiguration(
    string PluginName,
    Dictionary<string, object> Settings
);

public abstract record NotificationRequest(
    string Message,
    Dictionary<string, object>? Metadata = null
);

public abstract record NotificationResponse(
    bool IsSuccess,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
);

public sealed record PluginAdminInfo(
    string PluginName,
    string Version,
    string Provider,
    bool IsEnabled,
    Dictionary<string, object> CurrentConfiguration,
    List<ConfigurationField> ConfigurationFields,
    DateTime LastConfigUpdate,
    string Status
);

public sealed record ConfigurationField(
    string Name,
    string Type,
    string Description,
    bool IsRequired,
    bool IsSecret,
    object? DefaultValue = null,
    object? CurrentValue = null
);

public sealed record PluginMetrics(
    string PluginName,
    DateTime StartTime,
    DateTime LastResetTime,
    long TotalRequests,
    long SuccessfulRequests,
    long FailedRequests,
    double SuccessRate,
    double AverageResponseTimeMs,
    long TotalBytesSent,
    Dictionary<string, long> ErrorCounts,
    Dictionary<string, object> CustomMetrics
);
