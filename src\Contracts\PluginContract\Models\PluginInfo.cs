using PluginContract.Enums;

namespace PluginContract.Models;

/// <summary>
///  Model for representing plugin information
/// </summary>
/// <param name="Name"> Name of the plugin </param>
/// <param name="Version"> Version of the plugin </param>
/// <param name="Description"> Description of the plugin </param>
/// <param name="Type"> Type of the plugin </param>
/// <param name="Author"> Author of the plugin </param>`
public sealed record PluginInfo(
    string Name,
    string Version,
    string Description,
    PluginType Type,
    string Author,
    bool IsEnabled = true
);

/// <summary>
///  Model for representing plugin configuration
/// </summary>
/// <param name="PluginName"> Name of the plugin </param>
/// <param name="Settings"> Settings of the plugin </param>
public sealed record PluginConfiguration(
    string PluginName,
    Dictionary<string, object> Settings
);
/// <summary>
///  Base model for representing a notification request
/// </summary>
/// <param name="Message"> Message of the notification </param>
/// <param name="Metadata"> Metadata of the notification </param>
public abstract record NotificationRequest(
    string Message,
    Dictionary<string, object>? Metadata = null
);
/// <summary>
///  Base model for representing a notification response
/// </summary>
/// <param name="IsSuccess"> Indicates whether the request was successful </param>
/// <param name="ErrorMessage"> Error message if the request was not successful </param>
/// <param name="ResponseData"> Response data if the request was successful </param>
public abstract record NotificationResponse(
    bool IsSuccess,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
);
