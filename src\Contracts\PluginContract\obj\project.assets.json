{"version": 3, "targets": {"net9.0": {"Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}}}, "libraries": {"Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"sha512": "lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"sha512": "+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"sha512": "g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.0": {"sha512": "N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "type": "package", "path": "microsoft.extensions.primitives/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["Microsoft.Extensions.Configuration.Abstractions >= 9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions >= 9.0.0", "Microsoft.Extensions.Logging.Abstractions >= 9.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\PluginContract\\PluginContract.csproj", "projectName": "PluginContract", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\PluginContract\\PluginContract.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\PluginContract\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.NETCore.App": "(, 2.1.32767]", "Microsoft.VisualBasic": "(, 10.4.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 5.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 9.0.32767]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 5.0.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.5.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 9.0.32767]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 9.0.32767]", "System.Formats.Tar": "(, 9.0.32767]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 5.0.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Pipelines": "(, 9.0.32767]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 4.6.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 5.0.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 9.0.32767]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 5.0.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 6.0.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 9.0.32767]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.7.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Runtime.WindowsRuntime": "(, 4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 4.6.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 4.4.32767]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 9.0.32767]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 9.0.32767]", "System.Text.Json": "(, 9.0.32767]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.Channels": "(, 9.0.32767]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 9.0.32767]", "System.Threading.Tasks.Extensions": "(, 5.0.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 5.0.32767]"}}}}}