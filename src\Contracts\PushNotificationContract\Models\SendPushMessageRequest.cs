using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FirebaseAdmin.Messaging;

namespace PushNotificationContract.Models;

/// <summary>
/// Represents a request to send a push notification to a mobile device.
/// </summary>
/// <remarks>
/// This contract defines the essential information required to send a push notification,
/// including the device token, title, and message content. The model supports conversion
/// to Firebase messaging format and includes comprehensive validation.
/// </remarks>
public sealed record SendPushMessageRequest
{
    /// <summary>
    /// Gets the unique device token for the target device.
    /// </summary>
    /// <remarks>
    /// This token is provided by the mobile platform (iOS/Android) and uniquely
    /// identifies the app installation on a specific device.
    /// </remarks>
    /// <example>dGVzdF90b2tlbl8xMjM0NTY3ODkw</example>
    [JsonPropertyName("deviceToken")]
    [Required(ErrorMessage = "Device token is required")]
    [StringLength(500, MinimumLength = 10, ErrorMessage = "Device token must be between 10 and 500 characters")]
    public required string DeviceToken { get; init; }

    /// <summary>
    /// Gets the title of the push notification.
    /// </summary>
    /// <remarks>
    /// This appears as the main heading in the notification and should be concise
    /// and attention-grabbing.
    /// </remarks>
    /// <example>New Message</example>
    [JsonPropertyName("title")]
    [Required(ErrorMessage = "Notification title is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Title must be between 1 and 100 characters")]
    public required string Title { get; init; }

    /// <summary>
    /// Gets the body message of the push notification.
    /// </summary>
    /// <remarks>
    /// This is the main content of the notification that provides details to the user.
    /// Keep it concise as mobile platforms may truncate long messages.
    /// </remarks>
    /// <example>You have received a new message from John Doe.</example>
    [JsonPropertyName("message")]
    [Required(ErrorMessage = "Notification message is required")]
    [StringLength(500, MinimumLength = 1, ErrorMessage = "Message must be between 1 and 500 characters")]
    public required string Message { get; init; }

    /// <summary>
    /// Gets optional additional data to include with the notification.
    /// </summary>
    /// <remarks>
    /// This data is passed to the mobile app when the notification is received
    /// and can be used for deep linking or custom handling.
    /// </remarks>
    [JsonPropertyName("data")]
    public Dictionary<string, string>? Data { get; init; }

    /// <summary>
    /// Gets the priority level of the notification.
    /// </summary>
    /// <remarks>
    /// High priority notifications are delivered immediately, while normal priority
    /// notifications may be batched for battery optimization.
    /// </remarks>
    [JsonPropertyName("priority")]
    [RegularExpression(@"^(high|normal)$", ErrorMessage = "Priority must be either 'high' or 'normal'")]
    public string Priority { get; init; } = "normal";

    /// <summary>
    /// Gets the badge count to display on the app icon (iOS only).
    /// </summary>
    [JsonPropertyName("badge")]
    [Range(0, 99999, ErrorMessage = "Badge count must be between 0 and 99999")]
    public int? Badge { get; init; }

    /// <summary>
    /// Converts the push message request to a Firebase Message object.
    /// </summary>
    /// <param name="request">The push message request to convert</param>
    /// <returns>A Firebase Message object ready for sending</returns>
    public static explicit operator Message(SendPushMessageRequest request)
    {
        var message = new Message()
        {
            Token = request.DeviceToken,
            Notification = new Notification()
            {
                Title = request.Title,
                Body = request.Message
            }
        };

        // Add custom data if provided
        if (request.Data?.Count > 0)
        {
            message.Data = request.Data;
        }

        // Set Android-specific options
        message.Android = new AndroidConfig()
        {
            Priority = request.Priority == "high" ? FirebaseAdmin.Messaging.Priority.High : FirebaseAdmin.Messaging.Priority.Normal,
            Notification = new AndroidNotification()
            {
                Title = request.Title,
                Body = request.Message
            }
        };

        // Set iOS-specific options
        message.Apns = new ApnsConfig()
        {
            Aps = new Aps()
            {
                Alert = new ApsAlert()
                {
                    Title = request.Title,
                    Body = request.Message
                },
                Badge = request.Badge
            }
        };

        return message;
    }
}

