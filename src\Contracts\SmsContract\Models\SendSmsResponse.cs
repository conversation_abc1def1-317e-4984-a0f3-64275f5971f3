using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace SmsContract.Models;

/// <summary>
/// Represents the response from an SMS send operation.
/// </summary>
/// <remarks>
/// This response provides information about the SMS sending attempt,
/// including success status, message identifiers, delivery status, and any error details.
/// </remarks>
public sealed record SendSmsResponse
{
    /// <summary>
    /// Gets a value indicating whether the SMS was successfully sent or queued.
    /// </summary>
    [JsonPropertyName("success")]
    public required bool Success { get; init; }

    /// <summary>
    /// Gets the unique identifier for this SMS message.
    /// </summary>
    /// <remarks>
    /// This ID can be used to track the SMS status, retrieve delivery information,
    /// or reference the message in support requests.
    /// </remarks>
    /// <example>sms_**********abcdef</example>
    [JsonPropertyName("messageId")]
    [StringLength(100, ErrorMessage = "Message ID cannot exceed 100 characters")]
    public string? MessageId { get; init; }

    /// <summary>
    /// Gets the provider-specific message identifier.
    /// </summary>
    /// <remarks>
    /// This is the ID returned by the SMS service provider (e.g., Twilio, BulkSMS).
    /// It may differ from the internal MessageId and can be used for provider-specific tracking.
    /// </remarks>
    /// <example>SM**********abcdef**********abcdef</example>
    [JsonPropertyName("providerMessageId")]
    [StringLength(200, ErrorMessage = "Provider message ID cannot exceed 200 characters")]
    public string? ProviderMessageId { get; init; }

    /// <summary>
    /// Gets the current status of the SMS.
    /// </summary>
    /// <remarks>
    /// Common statuses include: Queued, Sent, Delivered, Failed, Undelivered
    /// </remarks>
    /// <example>Queued</example>
    [JsonPropertyName("status")]
    [StringLength(50, ErrorMessage = "Status cannot exceed 50 characters")]
    public string? Status { get; init; }

    /// <summary>
    /// Gets the error message if the SMS sending failed.
    /// </summary>
    /// <example>Invalid phone number format</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Gets the timestamp when the SMS was processed.
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// Gets the name of the provider used to send the SMS.
    /// </summary>
    /// <example>Twilio</example>
    [JsonPropertyName("provider")]
    [StringLength(50, ErrorMessage = "Provider name cannot exceed 50 characters")]
    public string? Provider { get; init; }

    /// <summary>
    /// Gets the phone number that was targeted.
    /// </summary>
    /// <remarks>
    /// This is useful for tracking which number received the SMS,
    /// especially in batch operations.
    /// </remarks>
    /// <example>+**********</example>
    [JsonPropertyName("phoneNumber")]
    [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
    public string? PhoneNumber { get; init; }

    /// <summary>
    /// Gets the cost of sending the SMS, if available from the provider.
    /// </summary>
    /// <example>0.0075</example>
    [JsonPropertyName("cost")]
    [Range(0, double.MaxValue, ErrorMessage = "Cost must be a positive number")]
    public decimal? Cost { get; init; }

    /// <summary>
    /// Gets the currency of the cost, if available.
    /// </summary>
    /// <example>USD</example>
    [JsonPropertyName("currency")]
    [StringLength(3, ErrorMessage = "Currency code cannot exceed 3 characters")]
    public string? Currency { get; init; }

    /// <summary>
    /// Gets additional metadata about the SMS sending operation.
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object>? Metadata { get; init; }

    /// <summary>
    /// Creates a successful SMS response.
    /// </summary>
    /// <param name="messageId">The message identifier</param>
    /// <param name="providerMessageId">The provider-specific message identifier</param>
    /// <param name="phoneNumber">The phone number that was targeted</param>
    /// <param name="provider">The provider used to send the SMS</param>
    /// <param name="status">The current status of the SMS</param>
    /// <param name="cost">The cost of sending the SMS</param>
    /// <param name="currency">The currency of the cost</param>
    /// <returns>A successful SendSmsResponse</returns>
    public static SendSmsResponse CreateSuccess(
        string messageId,
        string? providerMessageId = null,
        string? phoneNumber = null,
        string? provider = null,
        string status = "Queued",
        decimal? cost = null,
        string? currency = null)
    {
        return new SendSmsResponse
        {
            Success = true,
            MessageId = messageId,
            ProviderMessageId = providerMessageId,
            PhoneNumber = phoneNumber,
            Status = status,
            Provider = provider,
            Cost = cost,
            Currency = currency,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a failed SMS response.
    /// </summary>
    /// <param name="errorMessage">The error message describing the failure</param>
    /// <param name="messageId">The message identifier, if available</param>
    /// <param name="phoneNumber">The phone number that was attempted</param>
    /// <param name="provider">The provider that was attempted</param>
    /// <returns>A failed SendSmsResponse</returns>
    public static SendSmsResponse CreateFailure(
        string errorMessage,
        string? messageId = null,
        string? phoneNumber = null,
        string? provider = null)
    {
        return new SendSmsResponse
        {
            Success = false,
            MessageId = messageId,
            PhoneNumber = phoneNumber,
            ErrorMessage = errorMessage,
            Status = "Failed",
            Provider = provider,
            Timestamp = DateTime.UtcNow
        };
    }
}