using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PluginContract.Interfaces;
using PluginContract.Models;

namespace PluginCore.Base;

public abstract class BaseNotificationPlugin : INotificationPlugin, IDisposable
{
    protected ILogger? Logger { get; private set; }
    protected IConfiguration? Configuration { get; private set; }
    private readonly Dictionary<string, object> _currentConfiguration = new();
    private readonly PluginMetricsTracker _metricsTracker = new();

    public abstract PluginInfo PluginInfo { get; }

    public virtual void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Default implementation - plugins can override
    }

    public virtual Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        // Default implementation - plugins should override for specific validation
        return Task.FromResult(true);
    }

    public virtual Task InitializeAsync(IConfiguration configuration)
    {
        Configuration = configuration;
        
        // Create logger if available
        var serviceProvider = new ServiceCollection()
            .AddLogging()
            .BuildServiceProvider();
        
        Logger = serviceProvider.GetService<ILoggerFactory>()?.CreateLogger(GetType());
        
        return Task.CompletedTask;
    }

    public abstract Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default);

    public virtual Task<bool> HealthCheckAsync()
    {
        // Default implementation - plugins can override for specific health checks
        return Task.FromResult(true);
    }

    public virtual Task<PluginAdminInfo> GetAdminInfoAsync()
    {
        var adminInfo = new PluginAdminInfo(
            PluginInfo.Name,
            PluginInfo.Version,
            GetProviderName(),
            PluginInfo.IsEnabled,
            _currentConfiguration,
            GetConfigurationFields(),
            _metricsTracker.LastConfigUpdate,
            GetPluginStatus()
        );
        return Task.FromResult(adminInfo);
    }

    public virtual Task<bool> UpdateConfigurationAsync(Dictionary<string, object> configuration)
    {
        try
        {
            _currentConfiguration.Clear();
            foreach (var kvp in configuration)
            {
                _currentConfiguration[kvp.Key] = kvp.Value;
            }
            _metricsTracker.LastConfigUpdate = DateTime.UtcNow;
            return Task.FromResult(true);
        }
        catch
        {
            return Task.FromResult(false);
        }
    }

    public virtual Task<PluginMetrics> GetMetricsAsync()
    {
        var metrics = new PluginMetrics(
            PluginInfo.Name,
            _metricsTracker.StartTime,
            _metricsTracker.LastResetTime,
            _metricsTracker.TotalRequests,
            _metricsTracker.SuccessfulRequests,
            _metricsTracker.FailedRequests,
            _metricsTracker.SuccessRate,
            _metricsTracker.AverageResponseTimeMs,
            _metricsTracker.TotalBytesSent,
            _metricsTracker.ErrorCounts,
            GetCustomMetrics()
        );
        return Task.FromResult(metrics);
    }

    public virtual Task<bool> ResetMetricsAsync()
    {
        _metricsTracker.Reset();
        return Task.FromResult(true);
    }

    protected virtual string GetConfigurationSection()
    {
        return $"Plugins:{PluginInfo.Name}";
    }

    protected virtual T? GetConfiguration<T>() where T : class
    {
        if (Configuration == null) return null;

        var section = Configuration.GetSection(GetConfigurationSection());
        return section.Get<T>();
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        // Base implementation - plugins can override for cleanup
    }

    protected virtual string GetProviderName() => PluginInfo.Type.ToString();
    protected virtual string GetPluginStatus() => PluginInfo.IsEnabled ? "Active" : "Disabled";
    protected virtual List<ConfigurationField> GetConfigurationFields() => new();
    protected virtual Dictionary<string, object> GetCustomMetrics() => new();

    protected void TrackRequest(bool success, double responseTimeMs, long bytesSent = 0, string? errorType = null)
    {
        _metricsTracker.TrackRequest(success, responseTimeMs, bytesSent, errorType);
    }
}
