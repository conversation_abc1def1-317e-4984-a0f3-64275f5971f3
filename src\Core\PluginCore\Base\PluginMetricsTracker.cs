using System.Collections.Concurrent;

namespace PluginCore.Base;

public class PluginMetricsTracker
{
    private readonly object _lock = new();
    private readonly ConcurrentDictionary<string, long> _errorCounts = new();
    private readonly List<double> _responseTimes = new();

    public DateTime StartTime { get; private set; } = DateTime.UtcNow;
    public DateTime LastResetTime { get; set; } = DateTime.UtcNow;
    public DateTime LastConfigUpdate { get; set; } = DateTime.UtcNow;
    public long TotalRequests { get; private set; }
    public long SuccessfulRequests { get; private set; }
    public long FailedRequests { get; private set; }
    public long TotalBytesSent { get; private set; }

    public double SuccessRate => TotalRequests == 0 ? 0 : (double)SuccessfulRequests / TotalRequests * 100;
    public double AverageResponseTimeMs
    {
        get
        {
            lock (_lock)
            {
                return _responseTimes.Count == 0 ? 0 : _responseTimes.Average();
            }
        }
    }

    public Dictionary<string, long> ErrorCounts => _errorCounts.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

    public void TrackRequest(bool success, double responseTimeMs, long bytesSent = 0, string? errorType = null)
    {
        lock (_lock)
        {
            TotalRequests++;
            TotalBytesSent += bytesSent;
            
            if (success)
            {
                SuccessfulRequests++;
            }
            else
            {
                FailedRequests++;
                if (!string.IsNullOrEmpty(errorType))
                {
                    _errorCounts.AddOrUpdate(errorType, 1, (key, value) => value + 1);
                }
            }

            _responseTimes.Add(responseTimeMs);
            
            // Keep only last 1000 response times to prevent memory issues
            if (_responseTimes.Count > 1000)
            {
                _responseTimes.RemoveAt(0);
            }
        }
    }

    public void Reset()
    {
        lock (_lock)
        {
            LastResetTime = DateTime.UtcNow;
            TotalRequests = 0;
            SuccessfulRequests = 0;
            FailedRequests = 0;
            TotalBytesSent = 0;
            _errorCounts.Clear();
            _responseTimes.Clear();
        }
    }
}
