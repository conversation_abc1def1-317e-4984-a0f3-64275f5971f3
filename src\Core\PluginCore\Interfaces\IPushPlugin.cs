using PushNotificationContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;

/// <summary>
/// Defines the contract for push notification plugins.
/// </summary>
/// <remarks>
/// This interface extends the base notification plugin interface to provide
/// push notification-specific functionality including single and bulk push sending,
/// message tracking, device token management, and configuration validation.
/// </remarks>
public interface IPushPlugin : INotificationPlugin
{
    /// <summary>
    /// Sends a single push notification message asynchronously.
    /// </summary>
    /// <param name="request">The push notification request containing device token, title, and message content</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous send operation, containing the notification response</returns>
    /// <exception cref="ArgumentNullException">Thrown when request is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// The device token must be valid and registered with the push notification service.
    /// Invalid tokens may result in delivery failures or token cleanup by the provider.
    /// </remarks>
    Task<NotificationResponse> SendAsync(SendPushMessageRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends multiple push notification messages in a single batch operation asynchronously.
    /// </summary>
    /// <param name="requests">The collection of push notification requests to send</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous bulk send operation, containing the notification response</returns>
    /// <exception cref="ArgumentNullException">Thrown when requests is null</exception>
    /// <exception cref="ArgumentException">Thrown when requests collection is empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// Bulk operations are more efficient for sending multiple push notifications and provide
    /// better error handling for individual message failures. Rate limiting may apply.
    /// </remarks>
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendPushMessageRequest> requests, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the current status of a previously sent push notification message.
    /// </summary>
    /// <param name="messageId">The unique identifier of the message to check</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous status check operation, containing the notification response with status information</returns>
    /// <exception cref="ArgumentException">Thrown when messageId is null or empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// Status information may include delivery status, device acknowledgment, and failure reasons.
    /// Not all push notification providers support detailed status tracking.
    /// </remarks>
    Task<NotificationResponse> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the message history for a specific device token.
    /// </summary>
    /// <param name="deviceToken">The device token to retrieve history for</param>
    /// <param name="pageSize">The maximum number of messages to return (optional)</param>
    /// <param name="pageToken">Token for pagination (optional)</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous history retrieval operation, containing the notification response with message history</returns>
    /// <exception cref="ArgumentException">Thrown when deviceToken is null, empty, or invalid</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// History includes sent notifications, delivery status, and timestamps.
    /// Results may be paginated for large datasets. Device token validity is checked.
    /// </remarks>
    Task<NotificationResponse> GetMessageHistoryAsync(string deviceToken, int? pageSize = null, string? pageToken = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Resends a previously sent push notification message using the same content and device token.
    /// </summary>
    /// <param name="messageId">The unique identifier of the message to resend</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous resend operation, containing the notification response</returns>
    /// <exception cref="ArgumentException">Thrown when messageId is null or empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured or the original message cannot be found</exception>
    /// <remarks>
    /// The resent message will have a new message ID but will use the same content as the original.
    /// Device token validity is re-checked before resending.
    /// </remarks>
    Task<NotificationResponse> ResendMessageAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates the current plugin configuration to ensure it's properly set up for sending push notifications.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous validation operation, returning true if configuration is valid</returns>
    /// <remarks>
    /// This method should verify API keys, certificates, connection settings, and any other required configuration.
    /// It may perform test API calls to validate connectivity and authentication.
    /// </remarks>
    Task<bool> ValidateConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates a device token to ensure it's properly formatted and potentially active.
    /// </summary>
    /// <param name="deviceToken">The device token to validate</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous validation operation, returning true if the token appears valid</returns>
    /// <remarks>
    /// This method performs format validation and may check with the provider for token validity.
    /// Invalid tokens should be removed from target lists to improve delivery rates.
    /// </remarks>
    Task<bool> ValidateDeviceTokenAsync(string deviceToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the maximum number of device tokens that can be targeted in a single bulk operation.
    /// </summary>
    /// <returns>The maximum bulk size, or null if unlimited</returns>
    int? MaxBulkSize { get; }

    /// <summary>
    /// Gets a value indicating whether the provider supports message status tracking.
    /// </summary>
    bool SupportsStatusTracking { get; }

    /// <summary>
    /// Gets a value indicating whether the provider supports message resending.
    /// </summary>
    bool SupportsResending { get; }

    /// <summary>
    /// Gets a value indicating whether the provider supports rich media in notifications.
    /// </summary>
    bool SupportsRichMedia { get; }

    /// <summary>
    /// Gets the supported platforms for this push notification provider.
    /// </summary>
    /// <returns>A collection of supported platforms (e.g., "iOS", "Android", "Web")</returns>
    IEnumerable<string> SupportedPlatforms { get; }

    /// <summary>
    /// Gets the maximum payload size supported by the provider in bytes.
    /// </summary>
    /// <returns>The maximum payload size, or null if unlimited</returns>
    int? MaxPayloadSize { get; }
}
