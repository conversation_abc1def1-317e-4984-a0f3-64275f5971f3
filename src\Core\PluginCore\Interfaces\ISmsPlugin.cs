using SmsContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;

public interface ISmsPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendAsync(SendSmsRequest request);
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendSmsRequest> requests);
    Task<NotificationResponse> GetMessageStatusAsync(string messageId);
    Task<NotificationResponse> GetMessageHistoryAsync(string phoneNumber);
    Task<NotificationResponse> ResendMessageAsync(string messageId);
    Task<bool> ValidateConfigurationAsync();
}
