using Microsoft.Extensions.Logging;
using PluginContract.Enums;
using PluginContract.Interfaces;
using PluginContract.Models;

namespace PluginCore.Services;

public sealed class NotificationService
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<NotificationService> _logger;

    public NotificationService(IPluginManager pluginManager, ILogger<NotificationService> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<NotificationResponse> SendEmailAsync(EmailRequest request, string? preferredPlugin = null)
    {
        var plugins = GetAvailablePlugins<IEmailPlugin>(PluginType.Email, preferredPlugin);
        
        foreach (var plugin in plugins)
        {
            try
            {
                _logger.LogInformation("Attempting to send email using plugin {PluginName}", plugin.PluginInfo.Name);
                var result = await plugin.SendEmailAsync(request);
                
                if (result.IsSuccess)
                {
                    _logger.LogInformation("Email sent successfully using plugin {PluginName}", plugin.PluginInfo.Name);
                    return result;
                }
                
                _logger.LogWarning("Email sending failed with plugin {PluginName}: {Error}", 
                    plugin.PluginInfo.Name, result.ErrorMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email with plugin {PluginName}", plugin.PluginInfo.Name);
            }
        }

        return new EmailResponse(false, ErrorMessage: "All email plugins failed");
    }

    public async Task<NotificationResponse> SendSmsAsync(SmsRequest request, string? preferredPlugin = null)
    {
        var plugins = GetAvailablePlugins<ISmsPlugin>(PluginType.Sms, preferredPlugin);
        
        foreach (var plugin in plugins)
        {
            try
            {
                _logger.LogInformation("Attempting to send SMS using plugin {PluginName}", plugin.PluginInfo.Name);
                var result = await plugin.SendSmsAsync(request);
                
                if (result.IsSuccess)
                {
                    _logger.LogInformation("SMS sent successfully using plugin {PluginName}", plugin.PluginInfo.Name);
                    return result;
                }
                
                _logger.LogWarning("SMS sending failed with plugin {PluginName}: {Error}", 
                    plugin.PluginInfo.Name, result.ErrorMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS with plugin {PluginName}", plugin.PluginInfo.Name);
            }
        }

        return new SmsResponse(false, ErrorMessage: "All SMS plugins failed");
    }

    public async Task<NotificationResponse> SendPushNotificationAsync(PushNotificationRequest request, string? preferredPlugin = null)
    {
        var plugins = GetAvailablePlugins<IPushNotificationPlugin>(PluginType.PushNotification, preferredPlugin);
        
        foreach (var plugin in plugins)
        {
            try
            {
                _logger.LogInformation("Attempting to send push notification using plugin {PluginName}", plugin.PluginInfo.Name);
                var result = await plugin.SendPushNotificationAsync(request);
                
                if (result.IsSuccess)
                {
                    _logger.LogInformation("Push notification sent successfully using plugin {PluginName}", plugin.PluginInfo.Name);
                    return result;
                }
                
                _logger.LogWarning("Push notification sending failed with plugin {PluginName}: {Error}", 
                    plugin.PluginInfo.Name, result.ErrorMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending push notification with plugin {PluginName}", plugin.PluginInfo.Name);
            }
        }

        return new PushNotificationResponse(false, ErrorMessage: "All push notification plugins failed");
    }

    private IEnumerable<T> GetAvailablePlugins<T>(PluginType type, string? preferredPlugin = null) 
        where T : class, INotificationPlugin
    {
        var plugins = _pluginManager.GetEnabledPluginsByType(type).OfType<T>().ToList();
        
        if (!string.IsNullOrEmpty(preferredPlugin))
        {
            var preferred = plugins.FirstOrDefault(p => p.PluginInfo.Name.Equals(preferredPlugin, StringComparison.OrdinalIgnoreCase));
            if (preferred != null)
            {
                // Return preferred plugin first, then others as fallback
                return new[] { preferred }.Concat(plugins.Where(p => p != preferred));
            }
        }
        
        return plugins;
    }
}
