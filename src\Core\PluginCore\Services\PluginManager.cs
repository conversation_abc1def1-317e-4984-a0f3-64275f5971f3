using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PluginCore.Interfaces;
using PluginCore.Models;
using PluginContract.Interfaces;
using System.Reflection;
using System.Runtime.Loader;
using System.Text.Json;

namespace PluginCore.Services;

public class PluginManager : IPluginManager
{
    private readonly ILogger<PluginManager> _logger;
    private readonly Dictionary<string, LoadedPlugin> _loadedPlugins = new();
    private readonly string _pluginsDirectory;

    public PluginManager(ILogger<PluginManager> logger, string pluginsDirectory = "Plugins")
    {
        _logger = logger;
        _pluginsDirectory = pluginsDirectory;

        if (!Directory.Exists(_pluginsDirectory))
        {
            Directory.CreateDirectory(_pluginsDirectory);
        }
    }

    public async Task<bool> LoadPluginAsync(string pluginPath)
    {
        try
        {
            _logger.LogInformation("Loading plugin from {PluginPath}", pluginPath);

            // Read manifest file
            var manifestPath = Path.Combine(pluginPath, "manifest.json");
            if (!File.Exists(manifestPath))
            {
                _logger.LogError("Manifest file not found at {ManifestPath}", manifestPath);
                return false;
            }

            var manifestJson = await File.ReadAllTextAsync(manifestPath);
            var manifest = JsonSerializer.Deserialize<PluginManifest>(manifestJson);

            if (manifest == null)
            {
                _logger.LogError("Failed to deserialize manifest from {ManifestPath}", manifestPath);
                return false;
            }

            // Check if plugin is already loaded
            var pluginKey = $"{manifest.Type}.{manifest.Provider}";
            if (_loadedPlugins.ContainsKey(pluginKey))
            {
                _logger.LogWarning("Plugin {PluginKey} is already loaded", pluginKey);
                return false;
            }

            // Load assembly
            var assemblyPath = Path.Combine(pluginPath, manifest.AssemblyName);
            if (!File.Exists(assemblyPath))
            {
                _logger.LogError("Assembly file not found at {AssemblyPath}", assemblyPath);
                return false;
            }

            var loadContext = new AssemblyLoadContext($"Plugin_{pluginKey}", true);
            var assembly = loadContext.LoadFromAssemblyPath(assemblyPath);

            // Find and instantiate the plugin
            var pluginType = assembly.GetType(manifest.EntryPoint);
            if (pluginType == null)
            {
                _logger.LogError("Entry point type {EntryPoint} not found in assembly", manifest.EntryPoint);
                loadContext.Unload();
                return false;
            }

            var pluginInstance = Activator.CreateInstance(pluginType) as INotificationPlugin;
            if (pluginInstance == null)
            {
                _logger.LogError("Failed to create instance of plugin type {PluginType}", pluginType.Name);
                loadContext.Unload();
                return false;
            }

            // Initialize plugin
            var configuration = CreateConfigurationFromManifest(manifest);
            await pluginInstance.InitializeAsync(configuration);

            // Store loaded plugin
            _loadedPlugins[pluginKey] = new LoadedPlugin
            {
                Manifest = manifest,
                Instance = pluginInstance,
                LoadContext = loadContext,
                LoadedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Successfully loaded plugin {PluginKey} version {Version}",
                pluginKey, manifest.Version);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load plugin from {PluginPath}", pluginPath);
            return false;
        }
    }

    public async Task<bool> UnloadPluginAsync(string pluginKey)
    {
        try
        {
            if (!_loadedPlugins.TryGetValue(pluginKey, out var loadedPlugin))
            {
                _logger.LogWarning("Plugin {PluginKey} is not loaded", pluginKey);
                return false;
            }

            _logger.LogInformation("Unloading plugin {PluginKey}", pluginKey);

            // Dispose plugin if it implements IDisposable
            if (loadedPlugin.Instance is IDisposable disposable)
            {
                disposable.Dispose();
            }

            // Unload assembly context
            loadedPlugin.LoadContext.Unload();

            // Remove from loaded plugins
            _loadedPlugins.Remove(pluginKey);

            _logger.LogInformation("Successfully unloaded plugin {PluginKey}", pluginKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unload plugin {PluginKey}", pluginKey);
            return false;
        }
    }

    public IEnumerable<PluginInfo> GetLoadedPlugins()
    {
        return _loadedPlugins.Select(kvp => new PluginInfo
        {
            Key = kvp.Key,
            Name = kvp.Value.Manifest.Name,
            Version = kvp.Value.Manifest.Version,
            Type = kvp.Value.Manifest.Type,
            Provider = kvp.Value.Manifest.Provider,
            IsEnabled = kvp.Value.Manifest.IsEnabled,
            LoadedAt = kvp.Value.LoadedAt
        });
    }

    public T? GetPlugin<T>(string pluginKey) where T : class, INotificationPlugin
    {
        if (_loadedPlugins.TryGetValue(pluginKey, out var loadedPlugin))
        {
            return loadedPlugin.Instance as T;
        }
        return null;
    }

    public IEnumerable<T> GetPluginsByType<T>() where T : class, INotificationPlugin
    {
        return _loadedPlugins.Values
            .Select(p => p.Instance as T)
            .Where(p => p != null)
            .Cast<T>();
    }

    public async Task LoadAllPluginsAsync()
    {
        var pluginDirectories = Directory.GetDirectories(_pluginsDirectory);

        foreach (var pluginDir in pluginDirectories)
        {
            await LoadPluginAsync(pluginDir);
        }
    }

    // Interface implementations required by IPluginManager
    public async Task LoadPluginsAsync()
    {
        await LoadAllPluginsAsync();
    }

    private async Task LoadPluginsFromDirectoryAsync(string directory)
    {
        var pluginDirectories = Directory.GetDirectories(directory);

        foreach (var pluginDir in pluginDirectories)
        {
            await LoadPluginAsync(pluginDir);
        }
    }

    public IEnumerable<INotificationPlugin> GetAllPlugins()
    {
        return _loadedPlugins.Values.Select(p => p.Instance);
    }

    public IEnumerable<INotificationPlugin> GetPluginsByType(PluginContract.Enums.PluginType type)
    {
        return _loadedPlugins.Values
            .Where(p => string.Equals(p.Manifest.Type, type.ToString(), StringComparison.OrdinalIgnoreCase))
            .Select(p => p.Instance);
    }

    public INotificationPlugin? GetPluginByName(string name)
    {
        var plugin = _loadedPlugins.Values.FirstOrDefault(p => p.Manifest.Name == name);
        return plugin?.Instance;
    }

    public IEnumerable<INotificationPlugin> GetEnabledPluginsByType(PluginContract.Enums.PluginType type)
    {
        return _loadedPlugins.Values
            .Where(p => string.Equals(p.Manifest.Type, type.ToString(), StringComparison.OrdinalIgnoreCase) && p.Manifest.IsEnabled)
            .Select(p => p.Instance);
    }

    public async Task SetPluginEnabledAsync(string pluginName, bool enabled)
    {
        var plugin = _loadedPlugins.Values.FirstOrDefault(p => p.Manifest.Name == pluginName);
        if (plugin != null)
        {
            plugin.Manifest.IsEnabled = enabled;
            _logger.LogInformation("Plugin {PluginName} {Status}", pluginName, enabled ? "enabled" : "disabled");
        }
    }

    public async Task<Dictionary<string, bool>> GetPluginHealthStatusAsync()
    {
        var healthStatus = new Dictionary<string, bool>();

        foreach (var plugin in _loadedPlugins.Values)
        {
            try
            {
                // Basic health check - plugin is loaded and enabled
                healthStatus[plugin.Manifest.Name] = plugin.Manifest.IsEnabled;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed for plugin {PluginName}", plugin.Manifest.Name);
                healthStatus[plugin.Manifest.Name] = false;
            }
        }

        return healthStatus;
    }

    public void Dispose()
    {
        foreach (var plugin in _loadedPlugins.Values)
        {
            if (plugin.Instance is IDisposable disposable)
            {
                disposable.Dispose();
            }
            plugin.LoadContext.Unload();
        }
        _loadedPlugins.Clear();
    }

    private IConfiguration CreateConfigurationFromManifest(PluginManifest manifest)
    {
        var configBuilder = new ConfigurationBuilder();

        // Convert plugin configuration items to a dictionary
        var configDict = new Dictionary<string, string>();
        foreach (var kvp in manifest.Configuration)
        {
            if (kvp.Value.DefaultValue != null)
            {
                configDict[kvp.Key] = kvp.Value.DefaultValue.ToString() ?? string.Empty;
            }
        }

        configBuilder.AddInMemoryCollection(configDict);
        return configBuilder.Build();
    }
}

public class LoadedPlugin
{
    public PluginManifest Manifest { get; set; } = new();
    public INotificationPlugin Instance { get; set; } = null!;
    public AssemblyLoadContext LoadContext { get; set; } = null!;
    public DateTime LoadedAt { get; set; }
}

public class PluginInfo
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public DateTime LoadedAt { get; set; }
}
