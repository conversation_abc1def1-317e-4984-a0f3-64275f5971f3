using EmailService.Library.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using MimeKit;
using MailKit.Security;

namespace EmailService.Library.Services;

public sealed class SmtpClientService : EmailService.Library.Interfaces.ISmtpClient
{
    private readonly MailSetting _mailSetting;
    private readonly ILogger<SmtpClientService> _logger;

    public SmtpClientService(IOptions<EmailServiceSettings> config, ILogger<SmtpClientService> logger)
    {
        _mailSetting = config.Value.MailSettings;
        _logger = logger;
    }

    public async Task SendAsync(MimeMessage message)
    {
        using var client = new MailKit.Net.Smtp.SmtpClient();
        
        try
        {
            _logger.LogDebug("Connecting to SMTP server {Host}:{Port}", _mailSetting.Host, _mailSetting.Port);
            
            await client.ConnectAsync(_mailSetting.Host, _mailSetting.Port, _mailSetting.UseSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTls);
            
            if (!string.IsNullOrEmpty(_mailSetting.Username))
            {
                await client.AuthenticateAsync(_mailSetting.Username, _mailSetting.Password);
            }
            
            await client.SendAsync(message);
            
            _logger.LogDebug("Email sent successfully via SMTP");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email via SMTP");
            throw;
        }
        finally
        {
            await client.DisconnectAsync(true);
        }
    }
}
