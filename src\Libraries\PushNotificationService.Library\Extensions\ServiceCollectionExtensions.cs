using PushNotificationService.Library.Interfaces;
using PushNotificationService.Library.Services;
using Microsoft.Extensions.DependencyInjection;

namespace PushNotificationService.Library.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddPushNotificationService(this IServiceCollection services)
    {
        services.AddScoped<IPushNotificationService, PushNotificationServiceImplementation>();
        
        return services;
    }
}
