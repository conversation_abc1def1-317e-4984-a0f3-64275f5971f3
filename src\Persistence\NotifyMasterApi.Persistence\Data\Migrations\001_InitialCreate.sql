-- Initial database schema for NotificationService
-- PostgreSQL database migration

-- Create notification_logs table
CREATE TABLE notification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id VARCHAR(255) NOT NULL UNIQUE,
    type INTEGER NOT NULL,
    recipient VARCHAR(500) NOT NULL,
    subject VARCHA<PERSON>(1000),
    content TEXT,
    status INTEGER NOT NULL DEFAULT 0,
    provider VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER NOT NULL DEFAULT 0,
    last_retry_at TIMESTAMP WITH TIME ZONE,
    user_id VARCHAR(255),
    correlation_id VARCHAR(255),
    error_message TEXT,
    response_data JSONB,
    metadata JSONB
);

-- <PERSON><PERSON> indexes for notification_logs
CREATE INDEX idx_notification_logs_message_id ON notification_logs(message_id);
CREATE INDEX idx_notification_logs_recipient ON notification_logs(recipient);
CREATE INDEX idx_notification_logs_type ON notification_logs(type);
CREATE INDEX idx_notification_logs_status ON notification_logs(status);
CREATE INDEX idx_notification_logs_created_at ON notification_logs(created_at);
CREATE INDEX idx_notification_logs_correlation_id ON notification_logs(correlation_id);
CREATE INDEX idx_notification_logs_user_id ON notification_logs(user_id);

-- Create notification_metrics table
CREATE TABLE notification_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type INTEGER NOT NULL,
    provider VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    total_sent INTEGER NOT NULL DEFAULT 0,
    total_delivered INTEGER NOT NULL DEFAULT 0,
    total_failed INTEGER NOT NULL DEFAULT 0,
    success_rate DOUBLE PRECISION NOT NULL DEFAULT 0,
    failure_rate DOUBLE PRECISION NOT NULL DEFAULT 0,
    average_response_time DOUBLE PRECISION NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for notification_metrics
CREATE UNIQUE INDEX idx_notification_metrics_unique ON notification_metrics(type, provider, date);
CREATE INDEX idx_notification_metrics_type ON notification_metrics(type);
CREATE INDEX idx_notification_metrics_provider ON notification_metrics(provider);
CREATE INDEX idx_notification_metrics_date ON notification_metrics(date);

-- Create notification_errors table
CREATE TABLE notification_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type INTEGER NOT NULL,
    provider VARCHAR(100) NOT NULL,
    error_code VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    recipient VARCHAR(500),
    message_id VARCHAR(255),
    stack_trace TEXT,
    request_data JSONB,
    occurred_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    severity INTEGER NOT NULL DEFAULT 2,
    is_resolved BOOLEAN NOT NULL DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by VARCHAR(255),
    resolution_notes TEXT
);

-- Create indexes for notification_errors
CREATE INDEX idx_notification_errors_type ON notification_errors(type);
CREATE INDEX idx_notification_errors_provider ON notification_errors(provider);
CREATE INDEX idx_notification_errors_error_code ON notification_errors(error_code);
CREATE INDEX idx_notification_errors_occurred_at ON notification_errors(occurred_at);
CREATE INDEX idx_notification_errors_severity ON notification_errors(severity);
CREATE INDEX idx_notification_errors_is_resolved ON notification_errors(is_resolved);
CREATE INDEX idx_notification_errors_message_id ON notification_errors(message_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for notification_metrics
CREATE TRIGGER update_notification_metrics_updated_at 
    BEFORE UPDATE ON notification_metrics 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial data or setup if needed
-- (This section can be used for seed data)

COMMENT ON TABLE notification_logs IS 'Stores all notification activity logs with status tracking';
COMMENT ON TABLE notification_metrics IS 'Aggregated metrics for notification performance by type and provider';
COMMENT ON TABLE notification_errors IS 'Error tracking and resolution for notification failures';

COMMENT ON COLUMN notification_logs.type IS '0=Email, 1=Sms, 2=PushMessage';
COMMENT ON COLUMN notification_logs.status IS '0=Pending, 1=Sent, 2=Delivered, 3=Failed, 4=Retrying';
COMMENT ON COLUMN notification_errors.severity IS '1=Low, 2=Medium, 3=High, 4=Critical';
