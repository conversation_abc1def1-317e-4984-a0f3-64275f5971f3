<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NotifyMasterApi.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:NotifyMasterApi.Infrastructure.Entities.NotificationError">
            <summary>
            Represents an error that occurred during notification processing.
            Used for error tracking, debugging, and system monitoring.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.Id">
            <summary>
            Gets or sets the unique identifier for the error entry.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.Type">
            <summary>
            Gets or sets the type of notification that caused the error.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.Provider">
            <summary>
            Gets or sets the provider that was being used when the error occurred.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.ErrorCode">
            <summary>
            Gets or sets the error code from the provider or system.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.ErrorMessage">
            <summary>
            Gets or sets the detailed error message.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.Recipient">
            <summary>
            Gets or sets the recipient that was being notified when the error occurred.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.MessageId">
            <summary>
            Gets or sets the message ID associated with the failed notification.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.StackTrace">
            <summary>
            Gets or sets the stack trace of the error for debugging purposes.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.RequestData">
            <summary>
            Gets or sets the request data that was being processed when the error occurred.
            Stored as JSON for flexible data structure.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.OccurredAt">
            <summary>
            Gets or sets when the error occurred.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.Severity">
            <summary>
            Gets or sets the severity level of the error.
            1=Low, 2=Medium, 3=High, 4=Critical
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.IsResolved">
            <summary>
            Gets or sets whether this error has been resolved.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.ResolvedAt">
            <summary>
            Gets or sets when this error was resolved.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.ResolvedBy">
            <summary>
            Gets or sets who resolved this error.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationError.ResolutionNotes">
            <summary>
            Gets or sets notes about the error resolution.
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Infrastructure.Entities.NotificationLog">
            <summary>
            Represents a notification log entry in the database.
            Tracks the lifecycle and status of notifications sent through the system.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.Id">
            <summary>
            Gets or sets the unique identifier for the notification log entry.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.MessageId">
            <summary>
            Gets or sets the unique message identifier from the notification provider.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.Type">
            <summary>
            Gets or sets the type of notification (Email, SMS, Push).
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.Recipient">
            <summary>
            Gets or sets the recipient of the notification.
            For email: email address, for SMS: phone number, for push: device token.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.Subject">
            <summary>
            Gets or sets the subject of the notification (primarily for emails).
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.Content">
            <summary>
            Gets or sets the content/body of the notification.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.Status">
            <summary>
            Gets or sets the current status of the notification.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.Provider">
            <summary>
            Gets or sets the provider used to send the notification.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.ErrorMessage">
            <summary>
            Gets or sets the error message if the notification failed.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.ResponseData">
            <summary>
            Gets or sets the response data from the notification provider.
            Stored as JSON for flexible data structure.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.CreatedAt">
            <summary>
            Gets or sets when the notification was created.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.SentAt">
            <summary>
            Gets or sets when the notification was sent.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.DeliveredAt">
            <summary>
            Gets or sets when the notification was delivered.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.FailedAt">
            <summary>
            Gets or sets when the notification failed.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.RetryCount">
            <summary>
            Gets or sets the number of retry attempts made.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.LastRetryAt">
            <summary>
            Gets or sets when the last retry attempt was made.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.CorrelationId">
            <summary>
            Gets or sets the correlation ID for tracking related operations.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.UserId">
            <summary>
            Gets or sets the user ID associated with the notification.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationLog.Metadata">
            <summary>
            Gets or sets additional metadata associated with the notification.
            Stored as JSON for flexible data structure.
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics">
            <summary>
            Represents aggregated metrics for notification performance and statistics.
            Used for monitoring and reporting notification system performance.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.Id">
            <summary>
            Gets or sets the unique identifier for the metrics entry.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.Type">
            <summary>
            Gets or sets the type of notification these metrics apply to.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.Provider">
            <summary>
            Gets or sets the provider these metrics apply to.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.Date">
            <summary>
            Gets or sets the date these metrics are for.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.TotalSent">
            <summary>
            Gets or sets the total number of notifications sent.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.TotalDelivered">
            <summary>
            Gets or sets the total number of notifications delivered successfully.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.TotalFailed">
            <summary>
            Gets or sets the total number of notifications that failed.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.TotalRetries">
            <summary>
            Gets or sets the total number of retry attempts made.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.AverageResponseTime">
            <summary>
            Gets or sets the average response time in milliseconds.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.SuccessRate">
            <summary>
            Gets or sets the success rate as a percentage (0-100).
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.FailureRate">
            <summary>
            Gets or sets the failure rate as a percentage (0-100).
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.CreatedAt">
            <summary>
            Gets or sets when this metrics entry was created.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Entities.NotificationMetrics.UpdatedAt">
            <summary>
            Gets or sets when this metrics entry was last updated.
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository">
            <summary>
            Repository interface for notification error operations.
            Provides data access methods for error tracking and management.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.AddAsync(NotifyMasterApi.Infrastructure.Entities.NotificationError,System.Threading.CancellationToken)">
            <summary>
            Adds a new error entry.
            </summary>
            <param name="error">The error to add.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The added error with generated ID.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.UpdateAsync(NotifyMasterApi.Infrastructure.Entities.NotificationError,System.Threading.CancellationToken)">
            <summary>
            Updates an existing error entry.
            </summary>
            <param name="error">The error to update.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The updated error.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets an error by its ID.
            </summary>
            <param name="id">The error ID.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The error if found, null otherwise.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.GetAsync(System.Nullable{PluginCore.Enums.NotificationType},System.String,System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets errors with optional filtering and pagination.
            </summary>
            <param name="type">Optional notification type filter.</param>
            <param name="provider">Optional provider filter.</param>
            <param name="severity">Optional severity filter.</param>
            <param name="isResolved">Optional resolution status filter.</param>
            <param name="fromDate">Optional start date filter.</param>
            <param name="toDate">Optional end date filter.</param>
            <param name="skip">Number of records to skip for pagination.</param>
            <param name="take">Number of records to take for pagination.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>List of errors matching the criteria.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.GetCountAsync(System.Nullable{PluginCore.Enums.NotificationType},System.String,System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets the count of errors matching the specified criteria.
            </summary>
            <param name="type">Optional notification type filter.</param>
            <param name="provider">Optional provider filter.</param>
            <param name="severity">Optional severity filter.</param>
            <param name="isResolved">Optional resolution status filter.</param>
            <param name="fromDate">Optional start date filter.</param>
            <param name="toDate">Optional end date filter.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The count of matching errors.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.GetCriticalUnresolvedAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets unresolved critical errors.
            </summary>
            <param name="take">Maximum number of records to return.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>List of unresolved critical errors.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.GetErrorStatsByProviderAsync(System.Nullable{PluginCore.Enums.NotificationType},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets error statistics grouped by provider.
            </summary>
            <param name="type">Optional notification type filter.</param>
            <param name="fromDate">Optional start date filter.</param>
            <param name="toDate">Optional end date filter.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Error statistics grouped by provider.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.MarkAsResolvedAsync(System.Guid,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Marks an error as resolved.
            </summary>
            <param name="id">The error ID.</param>
            <param name="resolvedBy">Who resolved the error.</param>
            <param name="resolutionNotes">Notes about the resolution.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>True if the error was found and marked as resolved, false otherwise.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IErrorRepository.DeleteOldErrorsAsync(System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Deletes old errors based on retention policy.
            </summary>
            <param name="olderThan">Delete errors older than this date.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Number of deleted records.</returns>
        </member>
        <member name="T:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository">
            <summary>
            Repository interface for notification metrics operations.
            Provides data access methods for metrics tracking and reporting.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository.AddAsync(NotifyMasterApi.Infrastructure.Entities.NotificationMetrics,System.Threading.CancellationToken)">
            <summary>
            Adds a new metrics entry.
            </summary>
            <param name="metrics">The metrics to add.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The added metrics with generated ID.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository.UpdateAsync(NotifyMasterApi.Infrastructure.Entities.NotificationMetrics,System.Threading.CancellationToken)">
            <summary>
            Updates an existing metrics entry.
            </summary>
            <param name="metrics">The metrics to update.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The updated metrics.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets metrics by ID.
            </summary>
            <param name="id">The metrics ID.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The metrics if found, null otherwise.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository.GetByTypeProviderDateAsync(PluginCore.Enums.NotificationType,System.String,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets metrics for a specific type, provider, and date.
            </summary>
            <param name="type">The notification type.</param>
            <param name="provider">The provider name.</param>
            <param name="date">The date.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The metrics if found, null otherwise.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository.GetAsync(System.Nullable{PluginCore.Enums.NotificationType},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets metrics with optional filtering and pagination.
            </summary>
            <param name="type">Optional notification type filter.</param>
            <param name="provider">Optional provider filter.</param>
            <param name="fromDate">Optional start date filter.</param>
            <param name="toDate">Optional end date filter.</param>
            <param name="skip">Number of records to skip for pagination.</param>
            <param name="take">Number of records to take for pagination.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>List of metrics matching the criteria.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository.GetAggregatedAsync(System.Nullable{PluginCore.Enums.NotificationType},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets aggregated metrics for a date range.
            </summary>
            <param name="type">Optional notification type filter.</param>
            <param name="provider">Optional provider filter.</param>
            <param name="fromDate">Start date for aggregation.</param>
            <param name="toDate">End date for aggregation.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Aggregated metrics summary.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository.GetTopProvidersAsync(System.Nullable{PluginCore.Enums.NotificationType},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets the top performing providers by success rate.
            </summary>
            <param name="type">Optional notification type filter.</param>
            <param name="fromDate">Optional start date filter.</param>
            <param name="toDate">Optional end date filter.</param>
            <param name="take">Number of top providers to return.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>List of top performing providers with their metrics.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IMetricsRepository.DeleteOldMetricsAsync(System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Deletes old metrics based on retention policy.
            </summary>
            <param name="olderThan">Delete metrics older than this date.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Number of deleted records.</returns>
        </member>
        <member name="T:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository">
            <summary>
            Repository interface for notification log operations.
            Provides data access methods for notification tracking and management.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository.AddAsync(NotifyMasterApi.Infrastructure.Entities.NotificationLog,System.Threading.CancellationToken)">
            <summary>
            Adds a new notification log entry.
            </summary>
            <param name="notificationLog">The notification log to add.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The added notification log with generated ID.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository.UpdateAsync(NotifyMasterApi.Infrastructure.Entities.NotificationLog,System.Threading.CancellationToken)">
            <summary>
            Updates an existing notification log entry.
            </summary>
            <param name="notificationLog">The notification log to update.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The updated notification log.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a notification log by its ID.
            </summary>
            <param name="id">The notification log ID.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The notification log if found, null otherwise.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository.GetByMessageIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a notification log by its message ID.
            </summary>
            <param name="messageId">The message ID.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The notification log if found, null otherwise.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository.GetAsync(System.Nullable{PluginCore.Enums.NotificationType},System.Nullable{PluginCore.Enums.NotificationStatus},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets notification logs with optional filtering and pagination.
            </summary>
            <param name="type">Optional notification type filter.</param>
            <param name="status">Optional status filter.</param>
            <param name="provider">Optional provider filter.</param>
            <param name="fromDate">Optional start date filter.</param>
            <param name="toDate">Optional end date filter.</param>
            <param name="skip">Number of records to skip for pagination.</param>
            <param name="take">Number of records to take for pagination.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>List of notification logs matching the criteria.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository.GetCountAsync(System.Nullable{PluginCore.Enums.NotificationType},System.Nullable{PluginCore.Enums.NotificationStatus},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets the count of notification logs matching the specified criteria.
            </summary>
            <param name="type">Optional notification type filter.</param>
            <param name="status">Optional status filter.</param>
            <param name="provider">Optional provider filter.</param>
            <param name="fromDate">Optional start date filter.</param>
            <param name="toDate">Optional end date filter.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The count of matching notification logs.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository.GetForRetryAsync(System.Int32,System.Nullable{System.TimeSpan},System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets notification logs that need to be retried.
            </summary>
            <param name="maxRetryCount">Maximum retry count to consider.</param>
            <param name="retryAfter">Only include notifications that haven't been retried within this timespan.</param>
            <param name="take">Maximum number of records to return.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>List of notification logs that need retry.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.INotificationRepository.DeleteOldLogsAsync(System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Deletes old notification logs based on retention policy.
            </summary>
            <param name="olderThan">Delete logs older than this date.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Number of deleted records.</returns>
        </member>
        <member name="T:NotifyMasterApi.Infrastructure.Interfaces.IUnitOfWork">
            <summary>
            Unit of Work pattern interface for managing database transactions.
            Provides access to all repositories and transaction management.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Interfaces.IUnitOfWork.Notifications">
            <summary>
            Gets the notification repository.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Interfaces.IUnitOfWork.Metrics">
            <summary>
            Gets the metrics repository.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Infrastructure.Interfaces.IUnitOfWork.Errors">
            <summary>
            Gets the error repository.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IUnitOfWork.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            Saves all changes made in this unit of work to the database.
            </summary>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The number of state entries written to the database.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IUnitOfWork.BeginTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            Begins a new database transaction.
            </summary>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The database transaction.</returns>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IUnitOfWork.CommitTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            Commits the current database transaction.
            </summary>
            <param name="cancellationToken">Cancellation token.</param>
        </member>
        <member name="M:NotifyMasterApi.Infrastructure.Interfaces.IUnitOfWork.RollbackTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            Rolls back the current database transaction.
            </summary>
            <param name="cancellationToken">Cancellation token.</param>
        </member>
    </members>
</doc>
