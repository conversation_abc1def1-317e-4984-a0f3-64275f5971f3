<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NotifyMasterApi.Persistence</name>
    </assembly>
    <members>
        <member name="T:NotifyMasterApi.Persistence.Data.NotificationDbContext">
            <summary>
            Entity Framework DbContext for the notification system.
            Manages database connections and entity configurations.
            </summary>
            <param name="options">The database context options.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Data.NotificationDbContext.#ctor(Microsoft.EntityFrameworkCore.DbContextOptions{NotifyMasterApi.Persistence.Data.NotificationDbContext})">
            <summary>
            Entity Framework DbContext for the notification system.
            Manages database connections and entity configurations.
            </summary>
            <param name="options">The database context options.</param>
        </member>
        <member name="P:NotifyMasterApi.Persistence.Data.NotificationDbContext.NotificationLogs">
            <summary>
            Gets or sets the notification logs DbSet.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Persistence.Data.NotificationDbContext.NotificationMetrics">
            <summary>
            Gets or sets the notification metrics DbSet.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Persistence.Data.NotificationDbContext.NotificationErrors">
            <summary>
            Gets or sets the notification errors DbSet.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Data.NotificationDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            Configures the model and entity relationships.
            </summary>
            <param name="modelBuilder">The model builder.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Data.NotificationDbContext.ConfigureNotificationLog(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            Configures the NotificationLog entity.
            </summary>
            <param name="modelBuilder">The model builder.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Data.NotificationDbContext.ConfigureNotificationMetrics(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            Configures the NotificationMetrics entity.
            </summary>
            <param name="modelBuilder">The model builder.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Data.NotificationDbContext.ConfigureNotificationError(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            Configures the NotificationError entity.
            </summary>
            <param name="modelBuilder">The model builder.</param>
        </member>
        <member name="T:NotifyMasterApi.Persistence.Extensions.ServiceCollectionExtensions">
            <summary>
            Extension methods for configuring persistence services.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Extensions.ServiceCollectionExtensions.AddPersistence(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Adds persistence services to the service collection.
            </summary>
            <param name="services">The service collection.</param>
            <param name="configuration">The configuration.</param>
            <returns>The service collection for chaining.</returns>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Extensions.ServiceCollectionExtensions.AddDatabaseWithFallback(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Adds database context with fallback to in-memory database.
            </summary>
            <param name="services">The service collection.</param>
            <param name="configuration">The configuration.</param>
            <returns>The service collection for chaining.</returns>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Extensions.ServiceCollectionExtensions.ConfigureInMemoryFallback(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures in-memory database fallback.
            </summary>
            <param name="services">The service collection.</param>
            <returns>The service collection for chaining.</returns>
        </member>
        <member name="T:NotifyMasterApi.Persistence.Repositories.ErrorRepository">
            <summary>
            Repository implementation for notification error operations.
            Provides data access methods for error tracking and management.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.#ctor(NotifyMasterApi.Persistence.Data.NotificationDbContext)">
            <summary>
            Initializes a new instance of the ErrorRepository.
            </summary>
            <param name="context">The database context.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.AddAsync(NotifyMasterApi.Infrastructure.Entities.NotificationError,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.UpdateAsync(NotifyMasterApi.Infrastructure.Entities.NotificationError,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.GetAsync(System.Nullable{PluginCore.Enums.NotificationType},System.String,System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.GetCountAsync(System.Nullable{PluginCore.Enums.NotificationType},System.String,System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.GetCriticalUnresolvedAsync(System.Int32,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.GetErrorStatsByProviderAsync(System.Nullable{PluginCore.Enums.NotificationType},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.MarkAsResolvedAsync(System.Guid,System.String,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.ErrorRepository.DeleteOldErrorsAsync(System.DateTime,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:NotifyMasterApi.Persistence.Repositories.MetricsRepository">
            <summary>
            Repository implementation for notification metrics operations.
            Provides data access methods for metrics tracking and reporting.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.#ctor(NotifyMasterApi.Persistence.Data.NotificationDbContext)">
            <summary>
            Initializes a new instance of the MetricsRepository.
            </summary>
            <param name="context">The database context.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.AddAsync(NotifyMasterApi.Infrastructure.Entities.NotificationMetrics,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.UpdateAsync(NotifyMasterApi.Infrastructure.Entities.NotificationMetrics,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.GetByTypeProviderDateAsync(PluginCore.Enums.NotificationType,System.String,System.DateTime,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.GetAsync(System.Nullable{PluginCore.Enums.NotificationType},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.GetAggregatedAsync(System.Nullable{PluginCore.Enums.NotificationType},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.GetTopProvidersAsync(System.Nullable{PluginCore.Enums.NotificationType},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.MetricsRepository.DeleteOldMetricsAsync(System.DateTime,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:NotifyMasterApi.Persistence.Repositories.NotificationRepository">
            <summary>
            Repository implementation for notification log operations.
            Provides data access methods for notification tracking and management.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.#ctor(NotifyMasterApi.Persistence.Data.NotificationDbContext)">
            <summary>
            Initializes a new instance of the NotificationRepository.
            </summary>
            <param name="context">The database context.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.AddAsync(NotifyMasterApi.Infrastructure.Entities.NotificationLog,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.UpdateAsync(NotifyMasterApi.Infrastructure.Entities.NotificationLog,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.GetByMessageIdAsync(System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.GetAsync(System.Nullable{PluginCore.Enums.NotificationType},System.Nullable{PluginCore.Enums.NotificationStatus},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.GetCountAsync(System.Nullable{PluginCore.Enums.NotificationType},System.Nullable{PluginCore.Enums.NotificationStatus},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.GetForRetryAsync(System.Int32,System.Nullable{System.TimeSpan},System.Int32,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.Repositories.NotificationRepository.DeleteOldLogsAsync(System.DateTime,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:NotifyMasterApi.Persistence.Services.DatabaseFallbackActivationService">
            <summary>
            Background service that activates database fallback mechanisms.
            Ensures the in-memory database service is properly initialized when the main database is unavailable.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.DatabaseFallbackActivationService.#ctor(System.IServiceProvider,Microsoft.Extensions.Logging.ILogger{NotifyMasterApi.Persistence.Services.DatabaseFallbackActivationService})">
            <summary>
            Initializes a new instance of the DatabaseFallbackActivationService.
            </summary>
            <param name="serviceProvider">The service provider.</param>
            <param name="logger">The logger.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.DatabaseFallbackActivationService.ExecuteAsync(System.Threading.CancellationToken)">
            <summary>
            Executes the background service.
            </summary>
            <param name="stoppingToken">The cancellation token.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.DatabaseFallbackActivationService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            Stops the background service.
            </summary>
            <param name="cancellationToken">The cancellation token.</param>
        </member>
        <member name="T:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService">
            <summary>
            In-memory database service for fallback scenarios.
            Provides basic data storage when the main database is unavailable.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.IsUsingFallback">
            <summary>
            Gets a value indicating whether the service is using fallback mode.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.DataCount">
            <summary>
            Gets the current data count.
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.OperationLog">
            <summary>
            Gets the operation log.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.LogOperation(System.String,System.String,System.String)">
            <summary>
            Logs an operation for debugging and monitoring.
            </summary>
            <param name="operation">The operation type.</param>
            <param name="details">Operation details.</param>
            <param name="status">Operation status.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.StoreNotificationLog(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Stores a notification log entry.
            </summary>
            <param name="notificationId">The notification ID.</param>
            <param name="type">The notification type.</param>
            <param name="recipient">The recipient.</param>
            <param name="status">The status.</param>
            <param name="errorMessage">Optional error message.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.StorePluginLog(System.String,System.String,System.String,System.String)">
            <summary>
            Stores a plugin log entry.
            </summary>
            <param name="pluginId">The plugin ID.</param>
            <param name="action">The action performed.</param>
            <param name="status">The status.</param>
            <param name="details">Optional details.</param>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.GetNotificationLogs(System.Int32)">
            <summary>
            Gets notification logs.
            </summary>
            <param name="limit">Maximum number of logs to return.</param>
            <returns>Collection of notification logs.</returns>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.GetPluginLogs(System.Int32)">
            <summary>
            Gets plugin logs.
            </summary>
            <param name="limit">Maximum number of logs to return.</param>
            <returns>Collection of plugin logs.</returns>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.GetAllData">
            <summary>
            Gets all stored data.
            </summary>
            <returns>Dictionary of all stored data.</returns>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.ClearData">
            <summary>
            Clears all stored data.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.TryGetData``1(System.String,``0@)">
            <summary>
            Tries to get data by key.
            </summary>
            <typeparam name="T">The data type.</typeparam>
            <param name="key">The key.</param>
            <param name="data">The retrieved data.</param>
            <returns>True if data was found, false otherwise.</returns>
        </member>
        <member name="M:NotifyMasterApi.Persistence.Services.InMemoryDatabaseService.StoreData(System.String,System.Object)">
            <summary>
            Stores data with a specific key.
            </summary>
            <param name="key">The key.</param>
            <param name="data">The data to store.</param>
        </member>
        <member name="T:NotifyMasterApi.Persistence.UnitOfWork">
            <summary>
            Unit of Work implementation for managing database transactions.
            Provides access to all repositories and transaction management.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.UnitOfWork.#ctor(NotifyMasterApi.Persistence.Data.NotificationDbContext)">
            <summary>
            Initializes a new instance of the UnitOfWork.
            </summary>
            <param name="context">The database context.</param>
        </member>
        <member name="P:NotifyMasterApi.Persistence.UnitOfWork.Notifications">
            <inheritdoc />
        </member>
        <member name="P:NotifyMasterApi.Persistence.UnitOfWork.Metrics">
            <inheritdoc />
        </member>
        <member name="P:NotifyMasterApi.Persistence.UnitOfWork.Errors">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.UnitOfWork.SaveChangesAsync(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.UnitOfWork.BeginTransactionAsync(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.UnitOfWork.CommitTransactionAsync(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.UnitOfWork.RollbackTransactionAsync(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:NotifyMasterApi.Persistence.UnitOfWork.Dispose">
            <summary>
            Disposes the unit of work and releases all resources.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Persistence.UnitOfWork.Dispose(System.Boolean)">
            <summary>
            Disposes the unit of work and releases resources.
            </summary>
            <param name="disposing">True if disposing managed resources.</param>
        </member>
    </members>
</doc>
