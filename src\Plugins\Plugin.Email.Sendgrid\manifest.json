{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Email plugin for SendGrid provider", "author": "BlackBee Software", "type": "Email", "provider": "Sendgrid", "assemblyName": "Plugin.Email.Sendgrid.dll", "entryPoint": "Plugin.Email.Sendgrid.SendgridPlugin", "dependencies": [{"name": "SendGrid", "version": "9.29.3", "isRequired": true}], "configuration": {"apiKey": {"type": "string", "description": "SendGrid API Key", "isRequired": true, "isSecret": true}, "fromEmail": {"type": "string", "description": "Default sender email address", "isRequired": true, "isSecret": false}, "fromName": {"type": "string", "description": "Default sender name", "isRequired": false, "isSecret": false}, "templateId": {"type": "string", "description": "Default template ID for transactional emails", "isRequired": false, "isSecret": false}, "enableClickTracking": {"type": "bool", "description": "Enable click tracking", "defaultValue": true, "isRequired": false, "isSecret": false}, "enableOpenTracking": {"type": "bool", "description": "Enable open tracking", "defaultValue": true, "isRequired": false, "isSecret": false}}, "supportedFeatures": ["SendEmail", "BulkEmail", "TemplateEmail", "EmailTracking", "EmailStatistics"], "minimumFrameworkVersion": "net9.0", "isEnabled": true, "priority": 100, "metadata": {"website": "https://sendgrid.com", "documentation": "https://docs.sendgrid.com/"}}