is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.TargetFrameworkVersion = v9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = NotifyMaster.Portal.Blazor
build_property.RootNamespace = NotifyMaster.Portal.Blazor
build_property.ProjectDir = C:\Users\<USER>\OneDrive\Documentos\NotificationService-master\src\Portal\NotifyMaster.Portal.Blazor\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\OneDrive\Documentos\NotificationService-master\src\Portal\NotifyMaster.Portal.Blazor
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/App.razor]
build_metadata.AdditionalFiles.TargetPath = QXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/Pages/Analytics.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQW5hbHl0aWNzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/Pages/Notifications.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTm90aWZpY2F0aW9ucy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/Pages/Plugins.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUGx1Z2lucy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/Shared/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE5hdk1lbnUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/Pages/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/Documentos/NotificationService-master/src/Portal/NotifyMaster.Portal.Blazor/Pages/_Host.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0hvc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 
