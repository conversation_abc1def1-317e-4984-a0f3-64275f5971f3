{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/site.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000450450450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2219"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=\""}, {"Name": "ETag", "Value": "W/\"AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 04:32:07 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9433"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=\""}, {"Name": "Last-Modified", "Value": "Sat, 28 Jun 2025 14:03:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E="}]}, {"Route": "css/site.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2219"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 04:32:07 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw="}]}, {"Route": "css/site.g0f0i3txqp.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000450450450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2219"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=\""}, {"Name": "ETag", "Value": "W/\"AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 04:32:07 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0f0i3txqp"}, {"Name": "integrity", "Value": "sha256-AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.g0f0i3txqp.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9433"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=\""}, {"Name": "Last-Modified", "Value": "Sat, 28 Jun 2025 14:03:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0f0i3txqp"}, {"Name": "integrity", "Value": "sha256-AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.g0f0i3txqp.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2219"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 04:32:07 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0f0i3txqp"}, {"Name": "integrity", "Value": "sha256-HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw="}, {"Name": "label", "Value": "css/site.css.gz"}]}]}