{"Version": 1, "Hash": "ofVeU56epOAXzsDURWRI4hq3aDBCpfSQpiMtp83d5QE=", "Source": "NotifyMaster.Portal.Blazor", "BasePath": "/", "Mode": "Root", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "NotifyMaster.Portal.Blazor\\wwwroot", "Source": "NotifyMaster.Portal.Blazor", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\obj\\Debug\\net9.0\\compressed\\zs6xn6sqko-{0}-g0f0i3txqp-g0f0i3txqp.gz", "SourceId": "NotifyMaster.Portal.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "css/site#[.{fingerprint=g0f0i3txqp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u4z1imk6w2", "Integrity": "HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\wwwroot\\css\\site.css", "FileLength": 2219, "LastWriteTime": "2025-06-29T04:32:07+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\wwwroot\\css\\site.css", "SourceId": "NotifyMaster.Portal.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\wwwroot\\", "BasePath": "/", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g0f0i3txqp", "Integrity": "AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 9433, "LastWriteTime": "2025-06-28T14:03:26+00:00"}], "Endpoints": [{"Route": "css/site.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\obj\\Debug\\net9.0\\compressed\\zs6xn6sqko-{0}-g0f0i3txqp-g0f0i3txqp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000450450450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2219"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=\""}, {"Name": "ETag", "Value": "W/\"AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 04:32:07 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E="}]}, {"Route": "css/site.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9433"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=\""}, {"Name": "Last-Modified", "Value": "Sat, 28 Jun 2025 14:03:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E="}]}, {"Route": "css/site.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\obj\\Debug\\net9.0\\compressed\\zs6xn6sqko-{0}-g0f0i3txqp-g0f0i3txqp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2219"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 04:32:07 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw="}]}, {"Route": "css/site.g0f0i3txqp.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\obj\\Debug\\net9.0\\compressed\\zs6xn6sqko-{0}-g0f0i3txqp-g0f0i3txqp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000450450450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2219"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=\""}, {"Name": "ETag", "Value": "W/\"AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 04:32:07 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0f0i3txqp"}, {"Name": "integrity", "Value": "sha256-AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.g0f0i3txqp.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9433"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E=\""}, {"Name": "Last-Modified", "Value": "Sat, 28 Jun 2025 14:03:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0f0i3txqp"}, {"Name": "integrity", "Value": "sha256-AGZ5b3tBP3rcBNXNYFCzzSFvHcwqiydSbMIOi5djn0E="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.g0f0i3txqp.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Portal\\NotifyMaster.Portal.Blazor\\obj\\Debug\\net9.0\\compressed\\zs6xn6sqko-{0}-g0f0i3txqp-g0f0i3txqp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2219"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 04:32:07 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0f0i3txqp"}, {"Name": "integrity", "Value": "sha256-HpJzK4YPpvMIvftIHHzSB6tl792vxUMr3W4Z10VN7Mw="}, {"Name": "label", "Value": "css/site.css.gz"}]}]}