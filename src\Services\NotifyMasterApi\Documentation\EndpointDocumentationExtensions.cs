using FastEndpoints;
using System.ComponentModel;

namespace NotifyMasterApi.Documentation;

/// <summary>
/// Extension methods for enhanced FastEndpoints documentation
/// </summary>
public static class EndpointDocumentationExtensions
{
    /// <summary>
    /// Configure comprehensive documentation for messaging endpoints
    /// </summary>
    public static void ConfigureMessagingEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string category,
        string[] tags,
        bool requiresAuth = false,
        string? exampleRequest = null,
        string? exampleResponse = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        if (method.ToUpper() == "GET")
            endpoint.Get(route);
        else if (method.ToUpper() == "POST")
            endpoint.Post(route);
        else if (method.ToUpper() == "PUT")
            endpoint.Put(route);
        else if (method.ToUpper() == "DELETE")
            endpoint.Delete(route);

        if (!requiresAuth)
            endpoint.AllowAnonymous();

        endpoint.Summary(s =>
        {
            s.Summary = summary;
            s.Description = $"""
                {description}
                
                ## 📋 Category
                {category}
                
                ## 🔧 Usage
                This endpoint is part of the **{category}** functionality of the NotificationService API.
                
                {(exampleRequest != null ? $"## 📝 Example Request\n```json\n{exampleRequest}\n```\n" : "")}
                {(exampleResponse != null ? $"## ✅ Example Response\n```json\n{exampleResponse}\n```\n" : "")}
                
                ## 📚 Related Endpoints
                - Health Check: `GET /api/health`
                - System Status: `GET /api/admin/system/status`
                - Plugin Management: `GET /api/plugins`
                """;

            // Standard response codes
            s.Responses[200] = "✅ Operation completed successfully";
            s.Responses[400] = "❌ Bad Request - Invalid input parameters";
            s.Responses[401] = "🔒 Unauthorized - Authentication required";
            s.Responses[403] = "🚫 Forbidden - Insufficient permissions";
            s.Responses[404] = "🔍 Not Found - Resource not found";
            s.Responses[429] = "⏱️ Too Many Requests - Rate limit exceeded";
            s.Responses[500] = "💥 Internal Server Error - Something went wrong";
            s.Responses[503] = "🚧 Service Unavailable - Service temporarily unavailable";

            // Add custom response descriptions based on endpoint type
            if (category.Contains("Messaging") || category.Contains("Email") || category.Contains("SMS") || category.Contains("Push"))
            {
                s.Responses[200] = "✅ Message sent successfully";
                s.Responses[400] = "❌ Invalid message format or missing required fields";
                s.Responses[422] = "📝 Validation Error - Message content validation failed";
            }
            else if (category.Contains("Plugin"))
            {
                s.Responses[200] = "✅ Plugin operation completed successfully";
                s.Responses[404] = "🔌 Plugin not found or not loaded";
                s.Responses[409] = "⚠️ Plugin conflict or dependency issue";
            }
            else if (category.Contains("Admin") || category.Contains("System"))
            {
                s.Responses[200] = "✅ System information retrieved successfully";
                s.Responses[403] = "🔒 Admin privileges required";
            }
        });

        endpoint.Tags(tags);
    }

    /// <summary>
    /// Configure documentation for admin endpoints
    /// </summary>
    public static void ConfigureAdminEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string[] additionalTags = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        var tags = new List<string> { "🔧 Administration", "System Management" };
        if (additionalTags != null)
            tags.AddRange(additionalTags);

        endpoint.ConfigureMessagingEndpoint(
            method, route, summary, description,
            "System Administration",
            tags.ToArray(),
            requiresAuth: true
        );
    }

    /// <summary>
    /// Configure documentation for plugin endpoints
    /// </summary>
    public static void ConfigurePluginEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string[] additionalTags = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        var tags = new List<string> { "🔌 Plugin Management", "Extensions" };
        if (additionalTags != null)
            tags.AddRange(additionalTags);

        endpoint.ConfigureMessagingEndpoint(
            method, route, summary, description,
            "Plugin Management",
            tags.ToArray()
        );
    }

    /// <summary>
    /// Configure documentation for health and monitoring endpoints
    /// </summary>
    public static void ConfigureHealthEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string[] additionalTags = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        var tags = new List<string> { "💚 Health & Monitoring", "System Status" };
        if (additionalTags != null)
            tags.AddRange(additionalTags);

        endpoint.ConfigureMessagingEndpoint(
            method, route, summary, description,
            "Health & Monitoring",
            tags.ToArray()
        );
    }

    /// <summary>
    /// Configure documentation for messaging endpoints (Email, SMS, Push)
    /// </summary>
    public static void ConfigureNotificationEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string notificationType,
        string[] additionalTags = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        var emoji = notificationType.ToLower() switch
        {
            "email" => "📧",
            "sms" => "📱",
            "push" => "🔔",
            "messaging" => "💬",
            _ => "📨"
        };

        var tags = new List<string> { $"{emoji} {notificationType}", "Messaging", "Notifications" };
        if (additionalTags != null)
            tags.AddRange(additionalTags);

        endpoint.ConfigureMessagingEndpoint(
            method, route, summary, description,
            $"{notificationType} Messaging",
            tags.ToArray()
        );
    }
}

/// <summary>
/// Documentation categories for endpoint organization
/// </summary>
public static class DocumentationCategories
{
    public const string Email = "📧 Email Services";
    public const string SMS = "📱 SMS Services";
    public const string Push = "🔔 Push Notifications";
    public const string Messaging = "💬 Messaging Services";
    public const string PluginManagement = "🔌 Plugin Management";
    public const string SystemAdmin = "🔧 System Administration";
    public const string HealthMonitoring = "💚 Health & Monitoring";
    public const string TenantManagement = "🏢 Tenant Management";
    public const string Webhooks = "🔗 Webhooks & Events";
    public const string Templates = "📄 Templates";
    public const string Advanced = "⚡ Advanced Features";
    public const string Setup = "🚀 Setup & Configuration";
}

/// <summary>
/// Common example requests and responses for documentation
/// </summary>
public static class DocumentationExamples
{
    public static class Email
    {
        public const string SendRequest = """
            {
              "to": "<EMAIL>",
              "from": "<EMAIL>",
              "subject": "Welcome to NotificationService",
              "htmlBody": "<h1>Welcome!</h1><p>Thank you for joining us.</p>",
              "plainTextBody": "Welcome! Thank you for joining us.",
              "category": "welcome"
            }
            """;

        public const string SendResponse = """
            {
              "success": true,
              "messageId": "msg_abc123def456",
              "timestamp": "2024-01-15T10:30:00Z"
            }
            """;
    }

    public static class SMS
    {
        public const string SendRequest = """
            {
              "phoneNumber": "+1234567890",
              "message": "Your verification code is: 123456",
              "from": "YourService"
            }
            """;

        public const string SendResponse = """
            {
              "success": true,
              "messageId": "sms_xyz789abc123",
              "timestamp": "2024-01-15T10:30:00Z"
            }
            """;
    }

    public static class Push
    {
        public const string SendRequest = """
            {
              "deviceToken": "device_token_here",
              "title": "New Message",
              "message": "You have a new notification",
              "imageUrl": "https://example.com/image.png",
              "data": {
                "action": "open_chat",
                "chatId": "chat_123"
              }
            }
            """;

        public const string SendResponse = """
            {
              "success": true,
              "messageId": "push_def456ghi789",
              "timestamp": "2024-01-15T10:30:00Z"
            }
            """;
    }
}
