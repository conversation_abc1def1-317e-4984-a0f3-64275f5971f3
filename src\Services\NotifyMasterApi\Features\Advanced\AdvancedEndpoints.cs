using FastEndpoints;
using NotifyMasterApi.Infrastructure;
using NotifyMasterApi.Services;
using System.Text.Json;

namespace NotifyMasterApi.Features.Advanced;

public class CreateRetryProfileRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Strategy { get; set; } = "exponential";
    public int MaxRetries { get; set; } = 3;
    public int InitialDelaySeconds { get; set; } = 30;
    public int MaxDelaySeconds { get; set; } = 3600;
    public double BackoffMultiplier { get; set; } = 2.0;
}

public class CreateRetryProfileEndpoint : Endpoint<CreateRetryProfileRequest, object>
{
    private readonly ILogger<CreateRetryProfileEndpoint> _logger;
    private static readonly Dictionary<string, RetryProfile> _retryProfiles = new();

    public CreateRetryProfileEndpoint(ILogger<CreateRetryProfileEndpoint> logger)
    {
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/retry-profiles");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Create retry profile";
            s.Description = "Define custom retry strategy presets";
            s.Responses[200] = "Retry profile created successfully";
            s.Responses[400] = "Invalid request";
        });
        Tags("Retry Profiles");
    }

    public override async Task HandleAsync(CreateRetryProfileRequest req, CancellationToken ct)
    {
        try
        {
            var profile = new RetryProfile
            {
                TenantId = req.TenantId,
                Name = req.Name,
                Strategy = req.Strategy,
                MaxRetries = req.MaxRetries,
                InitialDelay = TimeSpan.FromSeconds(req.InitialDelaySeconds),
                MaxDelay = TimeSpan.FromSeconds(req.MaxDelaySeconds),
                BackoffMultiplier = req.BackoffMultiplier
            };

            _retryProfiles[profile.Id] = profile;

            await SendOkAsync(new
            {
                Profile = profile,
                Message = "Retry profile created successfully",
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating retry profile");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetRetryProfilesRequest
{
    public string? TenantId { get; set; }
}

public class GetRetryProfilesEndpoint : Endpoint<GetRetryProfilesRequest, object>
{
    private static readonly Dictionary<string, RetryProfile> _retryProfiles = new();

    public override void Configure()
    {
        Get("/api/retry-profiles");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get retry profiles";
            s.Description = "Get list of retry strategy presets";
            s.Responses[200] = "Retry profiles retrieved successfully";
        });
        Tags("Retry Profiles");
    }

    public override async Task HandleAsync(GetRetryProfilesRequest req, CancellationToken ct)
    {
        var profiles = _retryProfiles.Values.AsEnumerable();

        if (!string.IsNullOrEmpty(req.TenantId))
        {
            profiles = profiles.Where(p => p.TenantId == req.TenantId);
        }

        await SendOkAsync(new
        {
            Profiles = profiles.ToList(),
            Count = profiles.Count(),
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}

public class CreateRegionConfigRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public Dictionary<string, string> ProviderPreferences { get; set; } = new();
    public int Priority { get; set; } = 0;
}

public class CreateRegionConfigEndpoint : Endpoint<CreateRegionConfigRequest, object>
{
    private readonly ILogger<CreateRegionConfigEndpoint> _logger;
    private static readonly Dictionary<string, RegionConfig> _regionConfigs = new();

    public CreateRegionConfigEndpoint(ILogger<CreateRegionConfigEndpoint> logger)
    {
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/regions");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Create region configuration";
            s.Description = "Define region-specific routing and provider preferences";
            s.Responses[200] = "Region configuration created successfully";
            s.Responses[400] = "Invalid request";
        });
        Tags("Multi-Region");
    }

    public override async Task HandleAsync(CreateRegionConfigRequest req, CancellationToken ct)
    {
        try
        {
            var config = new RegionConfig
            {
                TenantId = req.TenantId,
                Region = req.Region,
                Country = req.Country,
                ProviderPreferences = req.ProviderPreferences,
                Priority = req.Priority
            };

            _regionConfigs[config.Id] = config;

            await SendOkAsync(new
            {
                Config = config,
                Message = "Region configuration created successfully",
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating region configuration");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetAccessAuditRequest
{
    public string? TenantId { get; set; }
    public string? UserId { get; set; }
    public string? Endpoint { get; set; }
    public string? IpAddress { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public class GetAccessAuditEndpoint : Endpoint<GetAccessAuditRequest, object>
{
    private readonly ILogger<GetAccessAuditEndpoint> _logger;
    private static readonly List<AccessAuditEntry> _auditEntries = new();

    public GetAccessAuditEndpoint(ILogger<GetAccessAuditEndpoint> logger)
    {
        _logger = logger;
        
        // Seed with sample data
        if (!_auditEntries.Any())
        {
            SeedAuditData();
        }
    }

    public override void Configure()
    {
        Get("/api/audit/access");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get access audit logs";
            s.Description = "List who accessed what and when with filtering options";
            s.Responses[200] = "Access audit logs retrieved successfully";
        });
        Tags("Audit");
    }

    public override async Task HandleAsync(GetAccessAuditRequest req, CancellationToken ct)
    {
        var entries = _auditEntries.AsEnumerable();

        if (!string.IsNullOrEmpty(req.TenantId))
        {
            entries = entries.Where(e => e.TenantId == req.TenantId);
        }

        if (!string.IsNullOrEmpty(req.UserId))
        {
            entries = entries.Where(e => e.UserId == req.UserId);
        }

        if (!string.IsNullOrEmpty(req.Endpoint))
        {
            entries = entries.Where(e => e.Endpoint.Contains(req.Endpoint, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(req.IpAddress))
        {
            entries = entries.Where(e => e.IpAddress == req.IpAddress);
        }

        if (req.StartDate.HasValue)
        {
            entries = entries.Where(e => e.Timestamp >= req.StartDate.Value);
        }

        if (req.EndDate.HasValue)
        {
            entries = entries.Where(e => e.Timestamp <= req.EndDate.Value);
        }

        var result = entries.OrderByDescending(e => e.Timestamp).Take(1000).ToList();

        await SendOkAsync(new
        {
            Entries = result,
            Count = result.Count,
            TotalCount = _auditEntries.Count,
            Timestamp = DateTime.UtcNow
        }, ct);
    }

    private void SeedAuditData()
    {
        var random = new Random();
        var endpoints = new[] { "/api/email/send", "/api/sms/send", "/api/plugins", "/api/tenants", "/api/users" };
        var users = new[] { "user1", "user2", "admin", "operator" };
        var ips = new[] { "*************", "*********", "***********" };

        for (int i = 0; i < 100; i++)
        {
            _auditEntries.Add(new AccessAuditEntry
            {
                TenantId = "default",
                UserId = users[random.Next(users.Length)],
                UserEmail = $"{users[random.Next(users.Length)]}@demo.com",
                Endpoint = endpoints[random.Next(endpoints.Length)],
                Method = random.Next(2) == 0 ? "GET" : "POST",
                IpAddress = ips[random.Next(ips.Length)],
                UserAgent = "NotificationPortal/2.0",
                StatusCode = random.Next(10) == 0 ? 400 : 200,
                Timestamp = DateTime.UtcNow.AddHours(-random.Next(168)), // Last week
                Duration = TimeSpan.FromMilliseconds(random.Next(50, 2000))
            });
        }
    }
}

public class RotateApiKeysRequest
{
    public string TenantId { get; set; } = string.Empty;
    public bool NotifyWebhook { get; set; } = true;
    public string? WebhookUrl { get; set; }
}

public class RotateApiKeysEndpoint : Endpoint<RotateApiKeysRequest, object>
{
    private readonly ILogger<RotateApiKeysEndpoint> _logger;
    private readonly IWebhookQueueService _webhookQueueService;

    public RotateApiKeysEndpoint(ILogger<RotateApiKeysEndpoint> logger, IWebhookQueueService webhookQueueService)
    {
        _logger = logger;
        _webhookQueueService = webhookQueueService;
    }

    public override void Configure()
    {
        Post("/api/keys/rotate");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Bulk rotate API keys";
            s.Description = "Rotate all API keys for a tenant and optionally notify linked systems";
            s.Responses[200] = "API keys rotated successfully";
            s.Responses[400] = "Invalid request";
        });
        Tags("API Keys");
    }

    public override async Task HandleAsync(RotateApiKeysRequest req, CancellationToken ct)
    {
        try
        {
            // Simulate key rotation
            var rotatedKeys = new List<object>();
            var keyCount = new Random().Next(3, 8);

            for (int i = 0; i < keyCount; i++)
            {
                var newKey = new
                {
                    Id = Guid.NewGuid().ToString(),
                    Key = $"nsk_{Guid.NewGuid():N}",
                    Name = $"API Key {i + 1}",
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddYears(1)
                };
                rotatedKeys.Add(newKey);
            }

            // Send webhook notification if requested
            if (req.NotifyWebhook && !string.IsNullOrEmpty(req.WebhookUrl))
            {
                var webhookPayload = new
                {
                    Event = "api_keys_rotated",
                    TenantId = req.TenantId,
                    RotatedAt = DateTime.UtcNow,
                    KeyCount = rotatedKeys.Count,
                    NewKeys = rotatedKeys.Select(k => new { ((dynamic)k).Id, ((dynamic)k).Name })
                };

                var webhook = new OutboundWebhookJob
                {
                    TenantId = req.TenantId,
                    Url = req.WebhookUrl,
                    Payload = JsonSerializer.Serialize(webhookPayload),
                    Headers = new Dictionary<string, string>
                    {
                        ["Content-Type"] = "application/json",
                        ["X-Event-Type"] = "api_keys_rotated"
                    }
                };

                await _webhookQueueService.QueueWebhookAsync(webhook);
            }

            await SendOkAsync(new
            {
                Success = true,
                Message = "API keys rotated successfully",
                RotatedKeys = rotatedKeys,
                NotificationSent = req.NotifyWebhook,
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rotating API keys for tenant {TenantId}", req.TenantId);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class SetThrottleModeRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string Behavior { get; set; } = "queue"; // reject, queue, delay, defer
    public int? RequestsPerSecond { get; set; }
    public int? QueueCapacity { get; set; }
    public int? DelaySeconds { get; set; }
}

public class SetThrottleModeEndpoint : Endpoint<SetThrottleModeRequest, object>
{
    private readonly ILogger<SetThrottleModeEndpoint> _logger;
    private static readonly Dictionary<string, ThrottleConfig> _throttleConfigs = new();

    public SetThrottleModeEndpoint(ILogger<SetThrottleModeEndpoint> logger)
    {
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/throttle/config");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Configure throttle behavior";
            s.Description = "Set how the system behaves when rate limits are exceeded";
            s.Responses[200] = "Throttle configuration updated successfully";
            s.Responses[400] = "Invalid request";
        });
        Tags("Throttling");
    }

    public override async Task HandleAsync(SetThrottleModeRequest req, CancellationToken ct)
    {
        try
        {
            var configKey = $"{req.TenantId}:{req.Channel}";
            
            var config = new ThrottleConfig
            {
                TenantId = req.TenantId,
                Channel = req.Channel,
                Behavior = req.Behavior,
                RequestsPerSecond = req.RequestsPerSecond ?? 10,
                QueueCapacity = req.QueueCapacity ?? 1000,
                DelayDuration = TimeSpan.FromSeconds(req.DelaySeconds ?? 1)
            };

            _throttleConfigs[configKey] = config;

            await SendOkAsync(new
            {
                Config = config,
                Message = "Throttle configuration updated successfully",
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting throttle configuration");
            await SendErrorsAsync(500, ct);
        }
    }
}
