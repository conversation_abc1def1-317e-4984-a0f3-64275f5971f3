using FastEndpoints;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Services;

namespace NotifyMasterApi.Features.Email;

public class GetEmailServiceStatusEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetEmailServiceStatusEndpoint> _logger;

    public GetEmailServiceStatusEndpoint(IPluginManager pluginManager, ILogger<GetEmailServiceStatusEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/email/admin/status");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get email service status";
            s.Description = "Get email service status and health information";
            s.Responses[200] = "Service status retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email Admin");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var emailPlugins = plugins.Where(p => p.Type == "Email" && p.IsEnabled).ToList();

            var status = new
            {
                Service = new
                {
                    Status = emailPlugins.Any() ? "Active" : "No Email Plugins Loaded",
                    LoadedPlugins = emailPlugins.Count,
                    AvailableProviders = emailPlugins.Select(p => p.Provider).ToList()
                },
                Plugins = emailPlugins.Select(p => new
                {
                    p.Name,
                    p.Version,
                    p.Provider,
                    p.IsEnabled,
                    Status = "Active"
                }).ToList(),
                Timestamp = DateTime.UtcNow
            };

            await SendOkAsync(status, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email service status");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetEmailProvidersAdminEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetEmailProvidersAdminEndpoint> _logger;

    public GetEmailProvidersAdminEndpoint(IPluginManager pluginManager, ILogger<GetEmailProvidersAdminEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/email/admin/providers");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get email providers for admin";
            s.Description = "Get list of email providers with admin information";
            s.Responses[200] = "Email providers retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email Admin");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var emailPlugins = plugins.Where(p => p.Type == "Email").ToList();

            var providers = new
            {
                Providers = emailPlugins.Select(p => new
                {
                    p.Name,
                    p.Version,
                    p.Provider,
                    p.IsEnabled,
                    p.LoadedAt,
                    Status = p.IsEnabled ? "Active" : "Disabled"
                }).ToList(),
                LoadedPlugins = emailPlugins,
                Timestamp = DateTime.UtcNow
            };

            await SendOkAsync(providers, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email providers");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class ConfigureEmailProviderRequest
{
    public string Provider { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class ConfigureEmailProviderEndpoint : Endpoint<ConfigureEmailProviderRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<ConfigureEmailProviderEndpoint> _logger;

    public ConfigureEmailProviderEndpoint(IPluginManager pluginManager, ILogger<ConfigureEmailProviderEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/email/admin/providers/{provider}/configure");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Configure email provider";
            s.Description = "Configure email provider settings";
            s.Responses[200] = "Provider configured successfully";
            s.Responses[400] = "Invalid configuration";
            s.Responses[404] = "Provider not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email Admin");
    }

    public override async Task HandleAsync(ConfigureEmailProviderRequest req, CancellationToken ct)
    {
        try
        {
            var plugin = await _pluginManager.GetPluginAsync(req.Provider);
            if (plugin == null)
            {
                await SendNotFoundAsync(ct);
                return;
            }

            var isValid = await _pluginManager.ValidatePluginConfigurationAsync(req.Provider, req.Configuration);
            if (!isValid)
            {
                await SendAsync(new { Error = "Invalid configuration provided" }, 400, ct);
                return;
            }

            var success = await _pluginManager.UpdatePluginConfigurationAsync(req.Provider, req.Configuration);
            if (success)
            {
                await SendOkAsync(new
                {
                    Success = true,
                    Message = $"Provider '{req.Provider}' configured successfully",
                    Provider = req.Provider,
                    Timestamp = DateTime.UtcNow
                }, ct);
            }
            else
            {
                await SendAsync(new { Error = "Failed to configure provider" }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring email provider {Provider}", req.Provider);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class SwitchEmailProviderRequest
{
    public string ProviderKey { get; set; } = string.Empty;
}

public class SwitchEmailProviderEndpoint : Endpoint<SwitchEmailProviderRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<SwitchEmailProviderEndpoint> _logger;

    public SwitchEmailProviderEndpoint(IPluginManager pluginManager, ILogger<SwitchEmailProviderEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/email/admin/switch-provider");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Switch email provider";
            s.Description = "Switch to a different email provider";
            s.Responses[200] = "Provider switched successfully";
            s.Responses[404] = "Provider not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email Admin");
    }

    public override async Task HandleAsync(SwitchEmailProviderRequest req, CancellationToken ct)
    {
        try
        {
            var plugin = await _pluginManager.GetPluginAsync(req.ProviderKey);
            if (plugin == null)
            {
                await SendNotFoundAsync(ct);
                return;
            }

            var success = await _pluginManager.EnablePluginAsync(req.ProviderKey);
            if (success)
            {
                await SendOkAsync(new
                {
                    Success = true,
                    Provider = req.ProviderKey,
                    Message = $"Switched to provider '{req.ProviderKey}'",
                    Timestamp = DateTime.UtcNow
                }, ct);
            }
            else
            {
                await SendAsync(new { Error = "Failed to switch provider" }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error switching email provider to {ProviderKey}", req.ProviderKey);
            await SendErrorsAsync(500, ct);
        }
    }
}
