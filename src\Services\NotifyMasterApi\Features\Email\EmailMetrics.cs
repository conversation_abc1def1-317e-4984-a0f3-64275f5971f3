using FastEndpoints;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Services;

namespace NotifyMasterApi.Features.Email;

public class GetEmailMetricsSummaryEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly INotificationLoggingService _loggingService;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetEmailMetricsSummaryEndpoint> _logger;

    public GetEmailMetricsSummaryEndpoint(
        INotificationLoggingService loggingService,
        IPluginManager pluginManager,
        ILogger<GetEmailMetricsSummaryEndpoint> logger)
    {
        _loggingService = loggingService;
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/email/metrics/summary");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get email metrics summary";
            s.Description = "Get summary metrics for email service";
            s.Responses[200] = "Summary metrics retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email Metrics");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var emailPlugins = plugins.Where(p => p.Type.ToString() == "Email" && p.IsEnabled).ToList();

            var pluginMetrics = new List<object>();
            foreach (var plugin in emailPlugins)
            {
                try
                {
                    var metrics = await _pluginManager.GetPluginMetricsAsync(plugin.Name ?? "");
                    pluginMetrics.Add(new
                    {
                        PluginName = plugin.Name,
                        Provider = plugin.Provider,
                        Metrics = metrics
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not get metrics for email plugin {PluginName}", plugin.Name);
                }
            }

            var summary = new
            {
                Service = new
                {
                    TotalPlugins = emailPlugins.Count,
                    ActivePlugins = emailPlugins.Count(p => p.IsEnabled),
                    Status = emailPlugins.Any() ? "Active" : "No Email Plugins"
                },
                PluginMetrics = pluginMetrics,
                Timestamp = DateTime.UtcNow
            };

            await SendOkAsync(summary, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email metrics summary");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetEmailMetricsDetailedRequest
{
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public string? Provider { get; set; }
}

public class GetEmailMetricsDetailedEndpoint : Endpoint<GetEmailMetricsDetailedRequest, object>
{
    private readonly INotificationLoggingService _loggingService;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetEmailMetricsDetailedEndpoint> _logger;

    public GetEmailMetricsDetailedEndpoint(
        INotificationLoggingService loggingService,
        IPluginManager pluginManager,
        ILogger<GetEmailMetricsDetailedEndpoint> logger)
    {
        _loggingService = loggingService;
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/email/metrics/detailed");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get detailed email metrics";
            s.Description = "Get detailed email metrics for a specific time period";
            s.Responses[200] = "Detailed metrics retrieved successfully";
            s.Responses[400] = "Invalid date range";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email Metrics");
    }

    public override async Task HandleAsync(GetEmailMetricsDetailedRequest req, CancellationToken ct)
    {
        try
        {
            if (req.From.HasValue && req.To.HasValue && req.From > req.To)
            {
                await SendAsync(new { Error = "Start date cannot be after end date" }, 400, ct);
                return;
            }

            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var emailPlugins = plugins.Where(p => p.Type.ToString() == "Email").ToList();

            if (!string.IsNullOrEmpty(req.Provider))
            {
                emailPlugins = emailPlugins.Where(p => 
                    p.Provider?.Equals(req.Provider, StringComparison.OrdinalIgnoreCase) == true).ToList();
            }

            var detailedMetrics = new List<object>();
            foreach (var plugin in emailPlugins)
            {
                try
                {
                    var metrics = await _pluginManager.GetPluginMetricsAsync(plugin.Name ?? "");
                    detailedMetrics.Add(new
                    {
                        PluginName = plugin.Name,
                        Provider = plugin.Provider,
                        Metrics = metrics,
                        Period = new { From = req.From, To = req.To }
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not get detailed metrics for email plugin {PluginName}", plugin.Name);
                }
            }

            var response = new
            {
                DetailedMetrics = detailedMetrics,
                Period = new { From = req.From, To = req.To },
                Provider = req.Provider,
                Timestamp = DateTime.UtcNow
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed email metrics");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetEmailMetricsMonthlyRequest
{
    public int Months { get; set; } = 12;
    public string? Provider { get; set; }
}

public class GetEmailMetricsMonthlyEndpoint : Endpoint<GetEmailMetricsMonthlyRequest, object>
{
    private readonly INotificationLoggingService _loggingService;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetEmailMetricsMonthlyEndpoint> _logger;

    public GetEmailMetricsMonthlyEndpoint(
        INotificationLoggingService loggingService,
        IPluginManager pluginManager,
        ILogger<GetEmailMetricsMonthlyEndpoint> logger)
    {
        _loggingService = loggingService;
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/email/metrics/monthly");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get monthly email statistics";
            s.Description = "Get monthly email statistics and trends";
            s.Responses[200] = "Monthly statistics retrieved successfully";
            s.Responses[400] = "Invalid months parameter";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email Metrics");
    }

    public override async Task HandleAsync(GetEmailMetricsMonthlyRequest req, CancellationToken ct)
    {
        try
        {
            if (req.Months <= 0 || req.Months > 24)
            {
                await SendAsync(new { Error = "Months must be between 1 and 24" }, 400, ct);
                return;
            }

            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMonths(-req.Months);

            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var emailPlugins = plugins.Where(p => p.Type.ToString() == "Email").ToList();

            if (!string.IsNullOrEmpty(req.Provider))
            {
                emailPlugins = emailPlugins.Where(p => 
                    p.Provider?.Equals(req.Provider, StringComparison.OrdinalIgnoreCase) == true).ToList();
            }

            var monthlyStats = new List<object>();
            foreach (var plugin in emailPlugins)
            {
                try
                {
                    var metrics = await _pluginManager.GetPluginMetricsAsync(plugin.Name ?? "");
                    monthlyStats.Add(new
                    {
                        PluginName = plugin.Name,
                        Provider = plugin.Provider,
                        MonthlyMetrics = metrics
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not get monthly stats for email plugin {PluginName}", plugin.Name);
                }
            }

            var response = new
            {
                MonthlyStatistics = monthlyStats,
                Period = new { StartDate = startDate, EndDate = endDate, Months = req.Months },
                Provider = req.Provider,
                Timestamp = DateTime.UtcNow
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monthly email statistics");
            await SendErrorsAsync(500, ct);
        }
    }
}
