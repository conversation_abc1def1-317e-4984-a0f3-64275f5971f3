using FastEndpoints;
using NotifyMasterApi.Gateways;
using NotificationContract.Models;

namespace NotifyMasterApi.Features.Email;

public class SendEmailRequest
{
    public string To { get; set; } = string.Empty;
    public string From { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string? Body { get; set; }
    public string? HtmlBody { get; set; }
    public string? PlainTextBody { get; set; }
    public string? Cc { get; set; }
    public string? Bcc { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
    public string? Category { get; set; }
}

public class SendEmailResponse
{
    public bool Success { get; set; }
    public string? MessageId { get; set; }
    public string? Error { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class SendEmailEndpoint : Endpoint<SendEmailRequest, SendEmailResponse>
{
    private readonly IEmailGateway _emailGateway;
    private readonly ILogger<SendEmailEndpoint> _logger;

    public SendEmailEndpoint(IEmailGateway emailGateway, ILogger<SendEmailEndpoint> logger)
    {
        _emailGateway = emailGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/email/send");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send email";
            s.Description = "Send an email message through available email plugins";
            s.Responses[200] = "Email sent successfully";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email");
    }

    public override async Task HandleAsync(SendEmailRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending email to {To}", req.To);

            var emailRequest = new EmailMessageRequest
            {
                To = req.To,
                From = req.From,
                Subject = req.Subject,
                Body = req.Body ?? req.PlainTextBody ?? req.HtmlBody ?? "",
                HtmlBody = req.HtmlBody,
                PlainTextBody = req.PlainTextBody,
                Cc = string.IsNullOrEmpty(req.Cc) ? null : new List<string> { req.Cc },
                Bcc = string.IsNullOrEmpty(req.Bcc) ? null : new List<string> { req.Bcc },
                Headers = req.Headers,
                Category = req.Category
            };

            var result = await _emailGateway.SendAsync(emailRequest);

            if (result.IsSuccess)
            {
                await SendOkAsync(new SendEmailResponse
                {
                    Success = true,
                    MessageId = result.MessageId
                }, ct);
            }
            else
            {
                await SendAsync(new SendEmailResponse
                {
                    Success = false,
                    Error = result.ErrorMessage
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email");
            await SendAsync(new SendEmailResponse
            {
                Success = false,
                Error = "Internal server error"
            }, 500, ct);
        }
    }
}

public class GetEmailProvidersEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IEmailGateway _emailGateway;
    private readonly ILogger<GetEmailProvidersEndpoint> _logger;

    public GetEmailProvidersEndpoint(IEmailGateway emailGateway, ILogger<GetEmailProvidersEndpoint> logger)
    {
        _emailGateway = emailGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/email/providers");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get email providers";
            s.Description = "Get list of available email providers";
            s.Responses[200] = "Providers retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var providers = await _emailGateway.GetProvidersAsync();
            await SendOkAsync(providers, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email providers");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class TestEmailProviderRequest
{
    public string Provider { get; set; } = string.Empty;
    public string? TestEmail { get; set; }
}

public class TestEmailProviderEndpoint : Endpoint<TestEmailProviderRequest, object>
{
    private readonly IEmailGateway _emailGateway;
    private readonly ILogger<TestEmailProviderEndpoint> _logger;

    public TestEmailProviderEndpoint(IEmailGateway emailGateway, ILogger<TestEmailProviderEndpoint> logger)
    {
        _emailGateway = emailGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/email/providers/{provider}/test");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Test email provider";
            s.Description = "Test a specific email provider";
            s.Responses[200] = "Provider test completed";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email");
    }

    public override async Task HandleAsync(TestEmailProviderRequest req, CancellationToken ct)
    {
        try
        {
            var result = await _emailGateway.TestProviderAsync(req.Provider, req.TestEmail);
            await SendOkAsync(result, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing email provider {Provider}", req.Provider);
            await SendErrorsAsync(500, ct);
        }
    }
}
