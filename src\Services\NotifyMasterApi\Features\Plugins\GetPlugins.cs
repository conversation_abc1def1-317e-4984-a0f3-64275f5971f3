using FastEndpoints;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Documentation;
using System.ComponentModel.DataAnnotations;

namespace NotifyMasterApi.Features.Plugins;

/// <summary>
/// Request model for retrieving plugins with optional filtering
/// </summary>
public class GetPluginsRequest
{
    /// <summary>
    /// Filter plugins by type (optional)
    /// </summary>
    /// <example>Email, SMS, Push, Messaging</example>
    public string? Type { get; set; }

    /// <summary>
    /// Filter plugins by enabled status (optional)
    /// </summary>
    /// <example>true</example>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// Filter plugins by provider (optional)
    /// </summary>
    /// <example>SendGrid, Twilio, FCM</example>
    public string? Provider { get; set; }

    /// <summary>
    /// Include detailed plugin information
    /// </summary>
    /// <example>false</example>
    public bool IncludeDetails { get; set; } = false;
}

public class GetPluginsResponse
{
    public List<PluginSummary> Plugins { get; set; } = new();
    public int TotalCount { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class PluginSummary
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? LoadedAt { get; set; }
}

public class GetPluginsEndpoint : Endpoint<GetPluginsRequest, GetPluginsResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginsEndpoint> _logger;

    public GetPluginsEndpoint(IPluginManager pluginManager, ILogger<GetPluginsEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigurePluginEndpoint(
            "GET",
            "/api/plugins",
            "Get All Plugins",
            """
            Retrieve a comprehensive list of all loaded plugins with their status, configuration, and capabilities.

            ## 🔌 Plugin Information
            - **Plugin Details**: Name, version, provider, and type
            - **Status Information**: Enabled/disabled state and health
            - **Configuration**: Current settings and capabilities
            - **Metrics**: Performance and usage statistics
            - **Dependencies**: Required services and connections

            ## 📋 Plugin Types
            - **Email Plugins**: SendGrid, Mailgun, SMTP, Amazon SES
            - **SMS Plugins**: Twilio, Kavenegar, Nexmo, Amazon SNS
            - **Push Plugins**: FCM, APNS, OneSignal, Web Push
            - **Messaging Plugins**: WhatsApp, Slack, Teams, Discord
            - **Utility Plugins**: Templates, Analytics, Webhooks

            ## 🔍 Filtering Options
            - Filter by plugin type (Email, SMS, Push, etc.)
            - Filter by enabled/disabled status
            - Filter by specific provider
            - Include/exclude detailed information

            ## 📊 Plugin Status
            - **Active**: Plugin is loaded and operational
            - **Inactive**: Plugin is loaded but disabled
            - **Error**: Plugin failed to load or has issues
            - **Loading**: Plugin is currently being initialized

            ## 🛠️ Management Operations
            Use this endpoint to:
            - Monitor plugin health and status
            - Identify available messaging providers
            - Check plugin configurations
            - Plan plugin management operations
            """,
            new[] { "System Management", "Configuration" }
        );
    }

    public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            
            var filteredPlugins = plugins.AsEnumerable();
            
            if (!string.IsNullOrEmpty(req.Type))
            {
                filteredPlugins = filteredPlugins.Where(p =>
                    p.Type.ToString().Equals(req.Type, StringComparison.OrdinalIgnoreCase));
            }
            
            if (req.IsEnabled.HasValue)
            {
                filteredPlugins = filteredPlugins.Where(p => p.IsEnabled == req.IsEnabled.Value);
            }

            var pluginSummaries = filteredPlugins.Select(p => new PluginSummary
            {
                Name = p.Name ?? "Unknown",
                Version = p.Version ?? "Unknown",
                Type = p.Type.ToString(),
                Provider = p.Provider ?? "Unknown",
                IsEnabled = p.IsEnabled,
                Status = p.IsEnabled ? "Active" : "Disabled",
                LoadedAt = p.LoadedAt
            }).ToList();

            await SendOkAsync(new GetPluginsResponse
            {
                Plugins = pluginSummaries,
                TotalCount = pluginSummaries.Count
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving plugins");
            await SendErrorsAsync(500, ct);
        }
    }
}
