using FastEndpoints;
using NotifyMasterApi.Interfaces;

namespace NotifyMasterApi.Features.Plugins;

public class GetPluginsRequest
{
    public string? Type { get; set; }
    public bool? IsEnabled { get; set; }
}

public class GetPluginsResponse
{
    public List<PluginSummary> Plugins { get; set; } = new();
    public int TotalCount { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class PluginSummary
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? LoadedAt { get; set; }
}

public class GetPluginsEndpoint : Endpoint<GetPluginsRequest, GetPluginsResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginsEndpoint> _logger;

    public GetPluginsEndpoint(IPluginManager pluginManager, ILogger<GetPluginsEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/plugins");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get all loaded plugins";
            s.Description = "Retrieve a list of all loaded plugins with their status and configuration";
            s.Responses[200] = "List of plugins retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Plugin Management");
    }

    public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            
            var filteredPlugins = plugins.AsEnumerable();
            
            if (!string.IsNullOrEmpty(req.Type))
            {
                filteredPlugins = filteredPlugins.Where(p => 
                    p.Type?.Equals(req.Type, StringComparison.OrdinalIgnoreCase) == true);
            }
            
            if (req.IsEnabled.HasValue)
            {
                filteredPlugins = filteredPlugins.Where(p => p.IsEnabled == req.IsEnabled.Value);
            }

            var pluginSummaries = filteredPlugins.Select(p => new PluginSummary
            {
                Name = p.Name ?? "Unknown",
                Version = p.Version ?? "Unknown",
                Type = p.Type ?? "Unknown",
                Provider = p.Provider ?? "Unknown",
                IsEnabled = p.IsEnabled,
                Status = p.IsEnabled ? "Active" : "Disabled",
                LoadedAt = p.LoadedAt
            }).ToList();

            await SendOkAsync(new GetPluginsResponse
            {
                Plugins = pluginSummaries,
                TotalCount = pluginSummaries.Count
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving plugins");
            await SendErrorsAsync(500, ct);
        }
    }
}
