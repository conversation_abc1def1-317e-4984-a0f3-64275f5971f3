using FastEndpoints;
using NotifyMasterApi.Gateways;
using NotificationContract.Models;

namespace NotifyMasterApi.Features.Push;

public class SendPushRequest
{
    public string DeviceToken { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public Dictionary<string, string>? Data { get; set; }
    public string? Platform { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class SendPushResponse
{
    public bool Success { get; set; }
    public string? MessageId { get; set; }
    public string? Error { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class SendPushEndpoint : Endpoint<SendPushRequest, SendPushResponse>
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<SendPushEndpoint> _logger;

    public SendPushEndpoint(IPushGateway pushGateway, ILogger<SendPushEndpoint> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/push/send");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send push notification";
            s.Description = "Send a push notification through available push plugins";
            s.Responses[200] = "Push notification sent successfully";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("Push");
    }

    public override async Task HandleAsync(SendPushRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending push notification to device {DeviceToken}", req.DeviceToken);

            var pushRequest = new PushMessageRequest
            {
                DeviceToken = req.DeviceToken,
                Title = req.Title,
                Message = req.Message,
                ImageUrl = req.ImageUrl,
                Data = req.Data,
                Platform = req.Platform,
                Metadata = req.Metadata
            };

            var result = await _pushGateway.SendAsync(pushRequest);

            if (result.IsSuccess)
            {
                await SendOkAsync(new SendPushResponse
                {
                    Success = true,
                    MessageId = result.MessageId
                }, ct);
            }
            else
            {
                await SendAsync(new SendPushResponse
                {
                    Success = false,
                    Error = result.ErrorMessage
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending push notification");
            await SendAsync(new SendPushResponse
            {
                Success = false,
                Error = "Internal server error"
            }, 500, ct);
        }
    }
}

public class SendBulkPushRequest
{
    public List<SendPushRequest> Messages { get; set; } = new();
}

public class SendBulkPushResponse
{
    public bool Success { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<SendPushResponse> Results { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class SendBulkPushEndpoint : Endpoint<SendBulkPushRequest, SendBulkPushResponse>
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<SendBulkPushEndpoint> _logger;

    public SendBulkPushEndpoint(IPushGateway pushGateway, ILogger<SendBulkPushEndpoint> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/push/send/bulk");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send bulk push notifications";
            s.Description = "Send multiple push notifications through available push plugins";
            s.Responses[200] = "Bulk push notifications processed";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("Push");
    }

    public override async Task HandleAsync(SendBulkPushRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending bulk push notifications to {Count} devices", req.Messages.Count);

            var bulkRequest = new BulkPushRequest
            {
                Messages = req.Messages.Select(m => new PushMessageRequest
                {
                    DeviceToken = m.DeviceToken,
                    Title = m.Title,
                    Message = m.Message,
                    ImageUrl = m.ImageUrl,
                    Data = m.Data,
                    Platform = m.Platform,
                    Metadata = m.Metadata
                }).ToList()
            };

            var result = await _pushGateway.SendBulkPushAsync(bulkRequest);

            var response = new SendBulkPushResponse
            {
                Success = result.IsSuccess,
                SuccessCount = result.SuccessCount,
                FailureCount = result.FailureCount,
                Results = result.Results?.Select(r => new SendPushResponse
                {
                    Success = r.IsSuccess,
                    MessageId = r.MessageId,
                    Error = r.ErrorMessage
                }).ToList() ?? new List<SendPushResponse>()
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk push notifications");
            await SendAsync(new SendBulkPushResponse
            {
                Success = false,
                FailureCount = req.Messages.Count
            }, 500, ct);
        }
    }
}

public class GetPushStatusRequest
{
    public string MessageId { get; set; } = string.Empty;
}

public class GetPushStatusEndpoint : Endpoint<GetPushStatusRequest, object>
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<GetPushStatusEndpoint> _logger;

    public GetPushStatusEndpoint(IPushGateway pushGateway, ILogger<GetPushStatusEndpoint> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/push/status");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get push notification status";
            s.Description = "Get the status of a push notification";
            s.Responses[200] = "Push status retrieved successfully";
            s.Responses[404] = "Message not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Push");
    }

    public override async Task HandleAsync(GetPushStatusRequest req, CancellationToken ct)
    {
        try
        {
            var status = await _pushGateway.GetMessageStatusAsync(req.MessageId);
            await SendOkAsync(status, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push status for {MessageId}", req.MessageId);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetPushPlatformsEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<GetPushPlatformsEndpoint> _logger;

    public GetPushPlatformsEndpoint(IPushGateway pushGateway, ILogger<GetPushPlatformsEndpoint> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/push/platforms");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get push platforms";
            s.Description = "Get list of available push notification platforms";
            s.Responses[200] = "Platforms retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Push");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var platforms = await _pushGateway.GetPlatformsAsync();
            await SendOkAsync(platforms, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push platforms");
            await SendErrorsAsync(500, ct);
        }
    }
}
