using FastEndpoints;
using NotifyMasterApi.Gateways;
using NotificationContract.Models;
using NotifyMasterApi.Documentation;
using System.ComponentModel.DataAnnotations;

namespace NotifyMasterApi.Features.SMS;

/// <summary>
/// Request model for sending an SMS message
/// </summary>
public class SendSmsRequest
{
    /// <summary>
    /// Recipient phone number in international format (required)
    /// </summary>
    /// <example>+1234567890</example>
    [Required(ErrorMessage = "Phone number is required")]
    [Phone(ErrorMessage = "Invalid phone number format")]
    [RegularExpression(@"^\+[1-9]\d{1,14}$", ErrorMessage = "Phone number must be in international format (+1234567890)")]
    public string PhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// SMS message content (required)
    /// </summary>
    /// <example>Your verification code is: 123456</example>
    [Required(ErrorMessage = "Message content is required")]
    [StringLength(1600, MinimumLength = 1, ErrorMessage = "Message must be between 1 and 1600 characters")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Sender ID or phone number (optional - uses default if not specified)
    /// </summary>
    /// <example>YourService</example>
    [StringLength(15, ErrorMessage = "Sender ID cannot exceed 15 characters")]
    public string? From { get; set; }

    /// <summary>
    /// Additional metadata for tracking and analytics
    /// </summary>
    /// <example>{"campaign": "verification", "userId": "12345"}</example>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Message type for provider-specific handling
    /// </summary>
    /// <example>transactional, promotional, otp</example>
    public string? MessageType { get; set; }

    /// <summary>
    /// Preferred SMS provider (optional - auto-selected if not specified)
    /// </summary>
    /// <example>Twilio, Kavenegar, Nexmo</example>
    public string? PreferredProvider { get; set; }
}

/// <summary>
/// Response model for SMS sending operation
/// </summary>
public class SendSmsResponse
{
    /// <summary>
    /// Indicates whether the SMS was sent successfully
    /// </summary>
    /// <example>true</example>
    public bool Success { get; set; }

    /// <summary>
    /// Unique identifier for the sent message (available when successful)
    /// </summary>
    /// <example>sms_xyz789abc123</example>
    public string? MessageId { get; set; }

    /// <summary>
    /// Error message if the operation failed
    /// </summary>
    /// <example>Invalid phone number format</example>
    public string? Error { get; set; }

    /// <summary>
    /// Timestamp when the operation was completed
    /// </summary>
    /// <example>2024-01-15T10:30:00Z</example>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// SMS provider used for sending
    /// </summary>
    /// <example>Twilio, Kavenegar, Nexmo</example>
    public string? Provider { get; set; }

    /// <summary>
    /// Delivery status information
    /// </summary>
    /// <example>queued, sent, delivered, failed</example>
    public string? DeliveryStatus { get; set; }

    /// <summary>
    /// Cost information for the SMS (if available)
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Currency for the cost (if available)
    /// </summary>
    /// <example>USD, EUR, IRR</example>
    public string? Currency { get; set; }

    /// <summary>
    /// Number of SMS segments used
    /// </summary>
    /// <example>1</example>
    public int? SegmentCount { get; set; }
}

public class SendSmsEndpoint : Endpoint<SendSmsRequest, SendSmsResponse>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<SendSmsEndpoint> _logger;

    public SendSmsEndpoint(ISmsGateway smsGateway, ILogger<SendSmsEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigureNotificationEndpoint(
            "POST",
            "/api/sms/send",
            "Send SMS Message",
            """
            Send an SMS message through the configured SMS providers with intelligent routing and delivery optimization.

            ## 🎯 Features
            - **Multi-Provider Support**: Automatic failover between SMS providers
            - **International Delivery**: Support for global SMS delivery
            - **Smart Routing**: Optimal provider selection based on destination
            - **Delivery Tracking**: Real-time delivery status updates
            - **Cost Optimization**: Automatic cost-effective provider selection

            ## 📋 Provider Support
            - Twilio (Global)
            - Kavenegar (Iran)
            - Nexmo/Vonage (Global)
            - Amazon SNS (Global)
            - Custom SMS Plugins

            ## 📱 Message Types
            - **Transactional**: OTP, verification codes, alerts
            - **Promotional**: Marketing messages, offers
            - **Informational**: Updates, notifications

            ## ⚡ Rate Limits
            - **Default**: 50 SMS/minute per API key
            - **Burst**: Up to 200 SMS in 10 seconds
            - **Daily**: 5,000 SMS per day (configurable)

            ## 🌍 International Support
            - 200+ countries supported
            - Local number support where available
            - Compliance with local regulations
            - Automatic character encoding (GSM 7-bit, UCS-2)
            """,
            "SMS",
            new[] { "Core Messaging", "Mobile Communication" }
        );
    }

    public override async Task HandleAsync(SendSmsRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending SMS to {PhoneNumber}", req.PhoneNumber);

            var smsRequest = new SmsMessageRequest
            {
                PhoneNumber = req.PhoneNumber,
                Message = req.Message,
                From = req.From,
                Metadata = req.Metadata
            };

            var result = await _smsGateway.SendAsync(smsRequest);

            if (result.IsSuccess)
            {
                await SendOkAsync(new SendSmsResponse
                {
                    Success = true,
                    MessageId = result.MessageId
                }, ct);
            }
            else
            {
                await SendAsync(new SendSmsResponse
                {
                    Success = false,
                    Error = result.ErrorMessage
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS");
            await SendAsync(new SendSmsResponse
            {
                Success = false,
                Error = "Internal server error"
            }, 500, ct);
        }
    }
}

public class SendBulkSmsRequest
{
    public List<SendSmsRequest> Messages { get; set; } = new();
}

public class SendBulkSmsResponse
{
    public bool Success { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<SendSmsResponse> Results { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class SendBulkSmsEndpoint : Endpoint<SendBulkSmsRequest, SendBulkSmsResponse>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<SendBulkSmsEndpoint> _logger;

    public SendBulkSmsEndpoint(ISmsGateway smsGateway, ILogger<SendBulkSmsEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/sms/send/bulk");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send bulk SMS";
            s.Description = "Send multiple SMS messages through available SMS plugins";
            s.Responses[200] = "Bulk SMS processed";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(SendBulkSmsRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending bulk SMS to {Count} recipients", req.Messages.Count);

            var bulkRequest = new BulkSmsRequest
            {
                Messages = req.Messages.Select(m => new SmsMessageRequest
                {
                    PhoneNumber = m.PhoneNumber,
                    Message = m.Message,
                    From = m.From,
                    Metadata = m.Metadata
                }).ToList()
            };

            var result = await _smsGateway.SendBulkAsync(bulkRequest);

            var response = new SendBulkSmsResponse
            {
                Success = result.IsSuccess,
                SuccessCount = result.SuccessCount,
                FailureCount = result.FailureCount,
                Results = result.Results?.Select(r => new SendSmsResponse
                {
                    Success = r.IsSuccess,
                    MessageId = r.MessageId,
                    Error = r.ErrorMessage
                }).ToList() ?? new List<SendSmsResponse>()
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk SMS");
            await SendAsync(new SendBulkSmsResponse
            {
                Success = false,
                FailureCount = req.Messages.Count
            }, 500, ct);
        }
    }
}

public class GetSmsStatusRequest
{
    public string MessageId { get; set; } = string.Empty;
}

public class GetSmsStatusEndpoint : Endpoint<GetSmsStatusRequest, object>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<GetSmsStatusEndpoint> _logger;

    public GetSmsStatusEndpoint(ISmsGateway smsGateway, ILogger<GetSmsStatusEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/sms/status");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get SMS status";
            s.Description = "Get the status of an SMS message";
            s.Responses[200] = "SMS status retrieved successfully";
            s.Responses[404] = "Message not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(GetSmsStatusRequest req, CancellationToken ct)
    {
        try
        {
            var status = await _smsGateway.GetMessageStatusAsync(req.MessageId);
            await SendOkAsync(status, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS status for {MessageId}", req.MessageId);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetSmsProvidersEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<GetSmsProvidersEndpoint> _logger;

    public GetSmsProvidersEndpoint(ISmsGateway smsGateway, ILogger<GetSmsProvidersEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/sms/providers");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get SMS providers";
            s.Description = "Get list of available SMS providers";
            s.Responses[200] = "Providers retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var providers = _smsGateway.GetAvailableProviders();
            await SendOkAsync(providers, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS providers");
            await SendErrorsAsync(500, ct);
        }
    }
}
