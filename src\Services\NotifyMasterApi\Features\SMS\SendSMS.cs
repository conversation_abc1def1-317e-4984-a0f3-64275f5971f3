using FastEndpoints;
using NotifyMasterApi.Gateways;
using NotificationContract.Models;

namespace NotifyMasterApi.Features.SMS;

public class SendSmsRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? From { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class SendSmsResponse
{
    public bool Success { get; set; }
    public string? MessageId { get; set; }
    public string? Error { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class SendSmsEndpoint : Endpoint<SendSmsRequest, SendSmsResponse>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<SendSmsEndpoint> _logger;

    public SendSmsEndpoint(ISmsGateway smsGateway, ILogger<SendSmsEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/sms/send");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send SMS";
            s.Description = "Send an SMS message through available SMS plugins";
            s.Responses[200] = "SMS sent successfully";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(SendSmsRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending SMS to {PhoneNumber}", req.PhoneNumber);

            var smsRequest = new SmsMessageRequest
            {
                PhoneNumber = req.PhoneNumber,
                Message = req.Message,
                From = req.From,
                Metadata = req.Metadata
            };

            var result = await _smsGateway.SendAsync(smsRequest);

            if (result.IsSuccess)
            {
                await SendOkAsync(new SendSmsResponse
                {
                    Success = true,
                    MessageId = result.MessageId
                }, ct);
            }
            else
            {
                await SendAsync(new SendSmsResponse
                {
                    Success = false,
                    Error = result.ErrorMessage
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS");
            await SendAsync(new SendSmsResponse
            {
                Success = false,
                Error = "Internal server error"
            }, 500, ct);
        }
    }
}

public class SendBulkSmsRequest
{
    public List<SendSmsRequest> Messages { get; set; } = new();
}

public class SendBulkSmsResponse
{
    public bool Success { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<SendSmsResponse> Results { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class SendBulkSmsEndpoint : Endpoint<SendBulkSmsRequest, SendBulkSmsResponse>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<SendBulkSmsEndpoint> _logger;

    public SendBulkSmsEndpoint(ISmsGateway smsGateway, ILogger<SendBulkSmsEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/sms/send/bulk");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send bulk SMS";
            s.Description = "Send multiple SMS messages through available SMS plugins";
            s.Responses[200] = "Bulk SMS processed";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(SendBulkSmsRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending bulk SMS to {Count} recipients", req.Messages.Count);

            var bulkRequest = new BulkSmsRequest
            {
                Messages = req.Messages.Select(m => new SmsMessageRequest
                {
                    PhoneNumber = m.PhoneNumber,
                    Message = m.Message,
                    From = m.From,
                    Metadata = m.Metadata
                }).ToList()
            };

            var result = await _smsGateway.SendBulkAsync(bulkRequest);

            var response = new SendBulkSmsResponse
            {
                Success = result.IsSuccess,
                SuccessCount = result.SuccessCount,
                FailureCount = result.FailureCount,
                Results = result.Results?.Select(r => new SendSmsResponse
                {
                    Success = r.IsSuccess,
                    MessageId = r.MessageId,
                    Error = r.ErrorMessage
                }).ToList() ?? new List<SendSmsResponse>()
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk SMS");
            await SendAsync(new SendBulkSmsResponse
            {
                Success = false,
                FailureCount = req.Messages.Count
            }, 500, ct);
        }
    }
}

public class GetSmsStatusRequest
{
    public string MessageId { get; set; } = string.Empty;
}

public class GetSmsStatusEndpoint : Endpoint<GetSmsStatusRequest, object>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<GetSmsStatusEndpoint> _logger;

    public GetSmsStatusEndpoint(ISmsGateway smsGateway, ILogger<GetSmsStatusEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/sms/status");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get SMS status";
            s.Description = "Get the status of an SMS message";
            s.Responses[200] = "SMS status retrieved successfully";
            s.Responses[404] = "Message not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(GetSmsStatusRequest req, CancellationToken ct)
    {
        try
        {
            var status = await _smsGateway.GetMessageStatusAsync(req.MessageId);
            await SendOkAsync(status, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS status for {MessageId}", req.MessageId);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetSmsProvidersEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<GetSmsProvidersEndpoint> _logger;

    public GetSmsProvidersEndpoint(ISmsGateway smsGateway, ILogger<GetSmsProvidersEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/sms/providers");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get SMS providers";
            s.Description = "Get list of available SMS providers";
            s.Responses[200] = "Providers retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var providers = _smsGateway.GetAvailableProviders();
            await SendOkAsync(providers, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS providers");
            await SendErrorsAsync(500, ct);
        }
    }
}
