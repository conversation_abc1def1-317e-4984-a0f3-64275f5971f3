using FastEndpoints;
using NotifyMasterApi.Infrastructure;
using NotifyMasterApi.Services;
using System.Text.Json;

namespace NotifyMasterApi.Features.Webhooks;

public class QueueWebhookRequest
{
    public string Url { get; set; } = string.Empty;
    public string Method { get; set; } = "POST";
    public Dictionary<string, string> Headers { get; set; } = new();
    public object? Payload { get; set; }
    public int MaxRetries { get; set; } = 3;
    public string? TenantId { get; set; }
}

public class QueueWebhookEndpoint : Endpoint<QueueWebhookRequest, object>
{
    private readonly IWebhookQueueService _webhookQueueService;
    private readonly ILogger<QueueWebhookEndpoint> _logger;

    public QueueWebhookEndpoint(IWebhookQueueService webhookQueueService, ILogger<QueueWebhookEndpoint> logger)
    {
        _webhookQueueService = webhookQueueService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/webhooks/queue");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Queue outbound webhook";
            s.Description = "Queue an outbound webhook for delivery with automatic retry";
            s.Responses[200] = "Webhook queued successfully";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("Webhooks");
    }

    public override async Task HandleAsync(QueueWebhookRequest req, CancellationToken ct)
    {
        try
        {
            var webhook = new OutboundWebhookJob
            {
                TenantId = req.TenantId ?? "default",
                Url = req.Url,
                Method = req.Method,
                Headers = req.Headers,
                Payload = req.Payload != null ? JsonSerializer.Serialize(req.Payload) : "",
                MaxRetries = req.MaxRetries
            };

            var webhookId = await _webhookQueueService.QueueWebhookAsync(webhook);

            await SendOkAsync(new
            {
                WebhookId = webhookId,
                Status = "Queued",
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error queueing webhook");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetWebhooksRequest
{
    public string? TenantId { get; set; }
    public string? Status { get; set; }
}

public class GetWebhooksEndpoint : Endpoint<GetWebhooksRequest, object>
{
    private readonly IWebhookQueueService _webhookQueueService;

    public GetWebhooksEndpoint(IWebhookQueueService webhookQueueService)
    {
        _webhookQueueService = webhookQueueService;
    }

    public override void Configure()
    {
        Get("/api/webhooks");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get webhooks";
            s.Description = "Get list of queued webhooks with optional filtering";
            s.Responses[200] = "Webhooks retrieved successfully";
        });
        Tags("Webhooks");
    }

    public override async Task HandleAsync(GetWebhooksRequest req, CancellationToken ct)
    {
        var webhooks = await _webhookQueueService.GetWebhooksAsync(req.TenantId, req.Status);
        await SendOkAsync(new { Webhooks = webhooks, Count = webhooks.Count }, ct);
    }
}

public class GetWebhookRequest
{
    public string WebhookId { get; set; } = string.Empty;
}

public class GetWebhookEndpoint : Endpoint<GetWebhookRequest, object>
{
    private readonly IWebhookQueueService _webhookQueueService;

    public GetWebhookEndpoint(IWebhookQueueService webhookQueueService)
    {
        _webhookQueueService = webhookQueueService;
    }

    public override void Configure()
    {
        Get("/api/webhooks/{webhookId}");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get webhook details";
            s.Description = "Get detailed information about a specific webhook";
            s.Responses[200] = "Webhook details retrieved successfully";
            s.Responses[404] = "Webhook not found";
        });
        Tags("Webhooks");
    }

    public override async Task HandleAsync(GetWebhookRequest req, CancellationToken ct)
    {
        var webhook = await _webhookQueueService.GetWebhookAsync(req.WebhookId);
        if (webhook == null)
        {
            await SendNotFoundAsync(ct);
            return;
        }

        var history = await _webhookQueueService.GetWebhookHistoryAsync(req.WebhookId);
        
        await SendOkAsync(new
        {
            Webhook = webhook,
            History = history,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}

public class RetryWebhookEndpoint : Endpoint<GetWebhookRequest, object>
{
    private readonly IWebhookQueueService _webhookQueueService;

    public RetryWebhookEndpoint(IWebhookQueueService webhookQueueService)
    {
        _webhookQueueService = webhookQueueService;
    }

    public override void Configure()
    {
        Post("/api/webhooks/{webhookId}/retry");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Retry webhook";
            s.Description = "Manually retry a failed webhook";
            s.Responses[200] = "Webhook retry initiated";
            s.Responses[404] = "Webhook not found";
            s.Responses[400] = "Webhook cannot be retried";
        });
        Tags("Webhooks");
    }

    public override async Task HandleAsync(GetWebhookRequest req, CancellationToken ct)
    {
        var success = await _webhookQueueService.RetryWebhookAsync(req.WebhookId);
        if (!success)
        {
            await SendAsync(new { Error = "Webhook not found or cannot be retried" }, 400, ct);
            return;
        }

        await SendOkAsync(new
        {
            Success = true,
            Message = "Webhook retry initiated",
            WebhookId = req.WebhookId,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}

public class GetEventsRequest
{
    public string? TenantId { get; set; }
    public string? Type { get; set; }
    public DateTime? Since { get; set; }
}

public class GetEventsEndpoint : Endpoint<GetEventsRequest, object>
{
    private readonly IEventStreamService _eventStreamService;

    public GetEventsEndpoint(IEventStreamService eventStreamService)
    {
        _eventStreamService = eventStreamService;
    }

    public override void Configure()
    {
        Get("/api/events");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get system events";
            s.Description = "Get system events with optional filtering";
            s.Responses[200] = "Events retrieved successfully";
        });
        Tags("Events");
    }

    public override async Task HandleAsync(GetEventsRequest req, CancellationToken ct)
    {
        var events = await _eventStreamService.GetEventsAsync(req.TenantId, req.Type, req.Since);
        await SendOkAsync(new
        {
            Events = events,
            Count = events.Count,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}

public class GetDeadLetterMessagesRequest
{
    public string? TenantId { get; set; }
    public string? Channel { get; set; }
}

public class GetDeadLetterMessagesEndpoint : Endpoint<GetDeadLetterMessagesRequest, object>
{
    private readonly IDeadLetterQueueService _deadLetterQueueService;

    public GetDeadLetterMessagesEndpoint(IDeadLetterQueueService deadLetterQueueService)
    {
        _deadLetterQueueService = deadLetterQueueService;
    }

    public override void Configure()
    {
        Get("/api/dead-letter");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get dead letter messages";
            s.Description = "Get messages that failed permanently";
            s.Responses[200] = "Dead letter messages retrieved successfully";
        });
        Tags("Dead Letter Queue");
    }

    public override async Task HandleAsync(GetDeadLetterMessagesRequest req, CancellationToken ct)
    {
        var messages = await _deadLetterQueueService.GetDeadLetterMessagesAsync(req.TenantId, req.Channel);
        var count = await _deadLetterQueueService.GetDeadLetterCountAsync(req.TenantId);
        
        await SendOkAsync(new
        {
            Messages = messages,
            TotalCount = count,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}

public class ReprocessDeadLetterRequest
{
    public string MessageId { get; set; } = string.Empty;
}

public class ReprocessDeadLetterEndpoint : Endpoint<ReprocessDeadLetterRequest, object>
{
    private readonly IDeadLetterQueueService _deadLetterQueueService;

    public ReprocessDeadLetterEndpoint(IDeadLetterQueueService deadLetterQueueService)
    {
        _deadLetterQueueService = deadLetterQueueService;
    }

    public override void Configure()
    {
        Post("/api/dead-letter/{messageId}/reprocess");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Reprocess dead letter message";
            s.Description = "Attempt to reprocess a failed message";
            s.Responses[200] = "Message reprocessing initiated";
            s.Responses[404] = "Message not found";
            s.Responses[400] = "Message cannot be reprocessed";
        });
        Tags("Dead Letter Queue");
    }

    public override async Task HandleAsync(ReprocessDeadLetterRequest req, CancellationToken ct)
    {
        var success = await _deadLetterQueueService.ReprocessMessageAsync(req.MessageId);
        if (!success)
        {
            await SendAsync(new { Error = "Message not found or cannot be reprocessed" }, 400, ct);
            return;
        }

        await SendOkAsync(new
        {
            Success = true,
            Message = "Message reprocessing initiated",
            MessageId = req.MessageId,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}
