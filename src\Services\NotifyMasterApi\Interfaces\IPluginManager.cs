using PluginContract.Models;

namespace NotifyMasterApi.Interfaces;

public interface IPluginManager
{
    Task<IEnumerable<PluginInfo>> GetLoadedPluginsAsync();
    Task<PluginInfo?> GetPluginAsync(string name);
    Task<bool> LoadPluginAsync(string pluginPath);
    Task<bool> UnloadPluginAsync(string name);
    Task<bool> EnablePluginAsync(string name);
    Task<bool> DisablePluginAsync(string name);
    Task<object> GetPluginConfigurationAsync(string name);
    Task<bool> UpdatePluginConfigurationAsync(string name, object configuration);
    Task<bool> ValidatePluginConfigurationAsync(string name, object configuration);
    Task<object> GetPluginHealthStatusAsync(string name);
    Task<object> GetAllPluginsHealthStatusAsync();
    Task<object> GetPluginMetricsAsync(string name);
    Task<bool> UploadPluginAsync(Stream pluginStream, string fileName);
    Task<bool> DeletePluginAsync(string name);
}
