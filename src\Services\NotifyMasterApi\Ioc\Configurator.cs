using System.Reflection;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using NotifyMasterApi.Data;
using NotifyMasterApi.Services;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Features.Setup;
using Serilog;
using Serilog.Events;
using Hangfire;
using Hangfire.InMemory;
using FastEndpoints;
using Scalar.AspNetCore;
using Hangfire.Dashboard;

namespace NotifyMasterApi.Ioc;

public static class Configurator
{
    public static void InjectService(this IServiceCollection services , IConfiguration configuration)
    {
        // Add FastEndpoints
        services.AddFastEndpoints();

        // Add OpenAPI with Scalar
        services.AddOpenApi();
        services.AddEndpointsApiExplorer();

        // Add Entity Framework with In-Memory database (simplified)
        services.AddDbContext<NotificationDbContext>(options =>
            options.UseInMemoryDatabase("NotificationService"));

        // Add Hangfire for background jobs
        services.AddHangfire(config => config
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseInMemoryStorage());

        services.AddHangfireServer();

        // Add simplified services
        services.AddScoped<IEmailGateway, EmailGateway>();
        services.AddScoped<ISmsGateway, SmsGateway>();
        services.AddScoped<IPushGateway, PushGateway>();
        services.AddScoped<INotificationLoggingService, NotificationLoggingService>();
        services.AddScoped<IPluginManager, RuntimePluginManager>();
        services.AddScoped<IPluginDetectionService, PluginDetectionService>();

        // Add advanced services
        services.AddScoped<IWebhookQueueService, WebhookQueueService>();
        services.AddScoped<IEventStreamService, EventStreamService>();
        services.AddScoped<IDeadLetterQueueService, DeadLetterQueueService>();
        services.AddScoped<IValidationService, ValidationService>();
        services.AddScoped<ITemplateRenderingService, TemplateRenderingService>();
        services.AddScoped<ISchedulingService, SchedulingService>();
        services.AddScoped<ISetupService, SetupService>();

        // Add HttpClient for plugins and webhooks
        services.AddHttpClient();

        // Add health checks
        services.AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy(), tags: new[] { "ready" });

        AddLogging(configuration);
    }
    
    public static void ConfigurePipeline(this WebApplication app)
    {
        // Configure Hangfire Dashboard
        app.UseHangfireDashboard("/hangfire", new DashboardOptions
        {
            Authorization = new[] { new HangfireAuthorizationFilter() }
        });

        // Configure FastEndpoints
        app.UseFastEndpoints();

        // Configure OpenAPI and Scalar UI
        app.MapOpenApi();
        app.MapScalarApiReference();

        // Add API endpoint for project configuration
        app.MapGet("/api/config", () => new
        {
            Name = "NotificationService",
            Version = "2.0.0",
            Description = "Modern plugin-based notification service with comprehensive admin and metrics",
            Architecture = "FastEndpoints + Plugin System",
            Features = new[] {
                "Email", "SMS", "Push", "WhatsApp", "Slack",
                "Plugin Management", "Multi-Tenancy", "Health Monitoring",
                "Metrics & Analytics", "Feature Detection"
            },
            Endpoints = new
            {
                Health = "/health/ready",
                Email = "/api/email/send",
                SMS = "/api/sms/send",
                Push = "/api/push/send",
                Plugins = "/api/plugins",
                Tenants = "/api/tenants",
                Features = "/api/system/features",
                Admin = "/api/admin",
                Documentation = "/scalar/v1"
            },
            Plugins = new
            {
                Email = new[] { "SendGrid", "Mailgun" },
                SMS = new[] { "Twilio", "BulkSMS", "Clickatel" },
                Push = new[] { "Firebase FCM" },
                Messaging = new[] { "WhatsApp Business", "Slack" },
                System = new[] { "MultiTenancy" }
            }
        }).WithTags("Configuration");

        // Health checks
        app.MapHealthChecks("/health/ready", new HealthCheckOptions()
        {
            Predicate = (check) => check.Tags.Contains("ready"),
        });
        app.MapHealthChecks("/health/live", new HealthCheckOptions());

        app.UseHttpsRedirection();
        app.UseAuthorization();
    }
    
    private static void AddLogging(IConfiguration configuration)
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .WriteTo.Debug()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();
    }
}

public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
{
    public bool Authorize(DashboardContext context)
    {
        // In production, implement proper authorization
        // For now, allow all access in development
        return true;
    }
}