using NotificationContract.Enums;

namespace NotifyMasterApi.Services;

public interface INotificationQueueService
{
    Task<string> QueueNotificationAsync(NotificationQueueItem item);
    Task<NotificationQueueItem?> DequeueNotificationAsync();
    Task<List<NotificationQueueItem>> GetPendingNotificationsAsync(int count = 10);
    Task MarkNotificationProcessedAsync(string queueId);
    Task MarkNotificationFailedAsync(string queueId, string errorMessage, int retryCount = 0);
    Task<int> GetQueueLengthAsync();
    Task<int> GetProcessingCountAsync();
    Task<int> GetFailedCountAsync();
    Task<List<NotificationQueueItem>> GetFailedNotificationsAsync(int count = 10);
    Task RequeueFailedNotificationAsync(string queueId);
    Task<int> RetryFailedNotificationsAsync();
    Task<int> ClearFailedNotificationsAsync();
}

public class NotificationQueueItem
{
    public string QueueId { get; set; } = string.Empty;
    public string MessageId { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public string Recipient { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? UserId { get; set; }
    public string? CorrelationId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public DateTime QueuedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public int RetryCount { get; set; }
    public int MaxRetries { get; set; } = 3;
    public string? ErrorMessage { get; set; }
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public DateTime? ScheduledFor { get; set; }
}

public enum NotificationPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}
