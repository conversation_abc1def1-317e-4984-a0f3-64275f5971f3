using NotifyMasterApi.Interfaces;
using PluginContract.Models;
using System.Reflection;
using System.Text.Json;

namespace NotifyMasterApi.Services;

/// <summary>
/// Runtime plugin manager that loads/unloads plugins dynamically without concrete references
/// </summary>
public class RuntimePluginManager : IPluginManager
{
    private readonly ILogger<RuntimePluginManager> _logger;
    private readonly Dictionary<string, RuntimePluginInstance> _loadedPlugins = new();
    private readonly string _pluginsDirectory;
    private readonly IConfiguration _configuration;

    public RuntimePluginManager(ILogger<RuntimePluginManager> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _pluginsDirectory = configuration["Plugins:Directory"] ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plugins");

        // Ensure plugins directory exists
        if (!Directory.Exists(_pluginsDirectory))
        {
            Directory.CreateDirectory(_pluginsDirectory);
            _logger.LogInformation("Created plugins directory: {Directory}", _pluginsDirectory);
        }
    }

    public async Task<IEnumerable<PluginInfo>> GetLoadedPluginsAsync()
    {
        return await Task.FromResult(_loadedPlugins.Values.Select(p => p.GetPluginInfo()));
    }

    public async Task<PluginInfo?> GetPluginAsync(string name)
    {
        if (_loadedPlugins.TryGetValue(name, out var plugin))
        {
            return await Task.FromResult(plugin.GetPluginInfo());
        }
        return await Task.FromResult<PluginInfo?>(null);
    }

    public async Task<bool> LoadPluginAsync(string pluginPath)
    {
        try
        {
            if (!File.Exists(pluginPath))
            {
                _logger.LogError("Plugin file not found: {PluginPath}", pluginPath);
                return false;
            }

            _logger.LogInformation("Loading plugin from: {PluginPath}", pluginPath);

            // Create isolated load context for the plugin
            var loadContext = new PluginLoadContext(pluginPath, isCollectible: true);

            // Load the plugin assembly
            var assembly = loadContext.LoadPluginAssembly();

            // Find the manifest
            var manifestResource = assembly.GetManifestResourceNames()
                .FirstOrDefault(name => name.EndsWith("manifest.json"));

            if (manifestResource == null)
            {
                _logger.LogError("No manifest.json found in plugin: {PluginPath}", pluginPath);
                loadContext.Unload();
                return false;
            }

            // Read and parse manifest
            using var stream = assembly.GetManifestResourceStream(manifestResource);
            using var reader = new StreamReader(stream!);
            var manifestJson = await reader.ReadToEndAsync();
            var manifest = JsonSerializer.Deserialize<PluginManifest>(manifestJson);

            if (manifest == null)
            {
                _logger.LogError("Invalid manifest in plugin: {PluginPath}", pluginPath);
                loadContext.Unload();
                return false;
            }

            // Check if plugin is already loaded
            if (_loadedPlugins.ContainsKey(manifest.Name))
            {
                _logger.LogWarning("Plugin already loaded: {PluginName}", manifest.Name);
                loadContext.Unload();
                return false;
            }

            // Find the plugin class
            var pluginType = assembly.GetType(manifest.EntryPoint);
            if (pluginType == null)
            {
                _logger.LogError("Plugin entry point not found: {EntryPoint}", manifest.EntryPoint);
                loadContext.Unload();
                return false;
            }

            // Create plugin instance using reflection (no concrete type references)
            object? pluginInstance;
            try
            {
                // Try to create instance with ILogger parameter
                pluginInstance = Activator.CreateInstance(pluginType, _logger);
            }
            catch
            {
                // Fallback: try parameterless constructor
                pluginInstance = Activator.CreateInstance(pluginType);
            }

            if (pluginInstance == null)
            {
                _logger.LogError("Failed to create plugin instance: {EntryPoint}", manifest.EntryPoint);
                loadContext.Unload();
                return false;
            }

            // Create runtime plugin wrapper
            var runtimePlugin = new RuntimePluginInstance(
                loadContext,
                pluginInstance,
                pluginType,
                manifest,
                assembly);

            // Initialize the plugin
            var initSuccess = await runtimePlugin.InitializeAsync(_configuration);
            if (!initSuccess)
            {
                _logger.LogWarning("Plugin initialization failed: {PluginName}", manifest.Name);
                runtimePlugin.Dispose();
                return false;
            }

            // Validate configuration
            var configValid = await runtimePlugin.ValidateConfigurationAsync(_configuration);
            if (!configValid)
            {
                _logger.LogWarning("Plugin configuration validation failed: {PluginName}", manifest.Name);
                runtimePlugin.Dispose();
                return false;
            }

            // Store the loaded plugin
            _loadedPlugins[manifest.Name] = runtimePlugin;

            _logger.LogInformation("Successfully loaded plugin: {PluginName} v{Version} ({Type})",
                manifest.Name, manifest.Version, manifest.Type);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading plugin: {PluginPath}", pluginPath);
            return false;
        }
    }

    public async Task<bool> UnloadPluginAsync(string name)
    {
        try
        {
            if (!_loadedPlugins.TryGetValue(name, out var plugin))
            {
                _logger.LogWarning("Plugin not found for unloading: {PluginName}", name);
                return false;
            }

            _logger.LogInformation("Unloading plugin: {PluginName}", name);

            // Dispose the plugin (this will unload the assembly context)
            plugin.Dispose();

            // Remove from loaded plugins
            _loadedPlugins.Remove(name);

            _logger.LogInformation("Successfully unloaded plugin: {PluginName}", name);
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unloading plugin: {PluginName}", name);
            return false;
        }
    }

    public async Task<bool> EnablePluginAsync(string name)
    {
        // Implementation for enabling plugin
        return await Task.FromResult(true);
    }

    public async Task<bool> DisablePluginAsync(string name)
    {
        // Implementation for disabling plugin
        return await Task.FromResult(true);
    }

    public async Task<object> GetPluginConfigurationAsync(string name)
    {
        // Implementation for getting plugin configuration
        return await Task.FromResult(new { });
    }

    public async Task<bool> UpdatePluginConfigurationAsync(string name, object configuration)
    {
        // Implementation for updating plugin configuration
        return await Task.FromResult(true);
    }

    public async Task<bool> ValidatePluginConfigurationAsync(string name, object configuration)
    {
        // Mock implementation
        _logger.LogInformation("Mock: Validating configuration for plugin {PluginName}", name);
        return await Task.FromResult(true);
    }

    public async Task<object> GetPluginHealthStatusAsync(string name)
    {
        // Mock implementation
        _logger.LogInformation("Mock: Getting health status for plugin {PluginName}", name);
        return await Task.FromResult(new { Status = "NotFound", IsHealthy = false });
    }

    public async Task<object> GetAllPluginsHealthStatusAsync()
    {
        // Mock implementation
        _logger.LogInformation("Mock: Getting health status for all plugins");
        return await Task.FromResult(new List<object>());
    }

    public async Task<object> GetPluginMetricsAsync(string name)
    {
        // Implementation for getting plugin metrics
        return await Task.FromResult(new
        {
            PluginName = name,
            RequestCount = 0,
            SuccessRate = 100.0,
            AverageResponseTime = 0.0
        });
    }

    public async Task<bool> UploadPluginAsync(Stream pluginStream, string fileName)
    {
        // Implementation for uploading plugin
        return await Task.FromResult(true);
    }

    public async Task<bool> DeletePluginAsync(string name)
    {
        try
        {
            // First unload the plugin if it's loaded
            if (_loadedPlugins.ContainsKey(name))
            {
                await UnloadPluginAsync(name);
            }

            // Find and delete the plugin file
            var pluginFiles = Directory.GetFiles(_pluginsDirectory, "*.dll", SearchOption.AllDirectories)
                .Where(f => Path.GetFileNameWithoutExtension(f).Contains(name, StringComparison.OrdinalIgnoreCase));

            foreach (var file in pluginFiles)
            {
                File.Delete(file);
                _logger.LogInformation("Deleted plugin file: {PluginFile}", file);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting plugin: {PluginName}", name);
            return false;
        }
    }

    /// <summary>
    /// Send notification through a specific plugin using runtime invocation
    /// </summary>
    public async Task<object?> SendNotificationAsync(string pluginName, object request, CancellationToken cancellationToken = default)
    {
        if (!_loadedPlugins.TryGetValue(pluginName, out var plugin))
        {
            _logger.LogWarning("Plugin not found: {PluginName}", pluginName);
            return null;
        }

        if (!plugin.IsEnabled)
        {
            _logger.LogWarning("Plugin is disabled: {PluginName}", pluginName);
            return null;
        }

        try
        {
            return await plugin.SendNotificationAsync(request, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification through plugin: {PluginName}", pluginName);
            return null;
        }
    }

    /// <summary>
    /// Get health status of all loaded plugins
    /// </summary>
    public async Task<Dictionary<string, bool>> GetPluginsHealthStatusAsync()
    {
        var healthStatus = new Dictionary<string, bool>();

        foreach (var plugin in _loadedPlugins.Values)
        {
            try
            {
                var isHealthy = await plugin.HealthCheckAsync();
                healthStatus[plugin.Name] = isHealthy;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking health for plugin: {PluginName}", plugin.Name);
                healthStatus[plugin.Name] = false;
            }
        }

        return healthStatus;
    }

    /// <summary>
    /// Get plugins by type (SMS, Email, Push)
    /// </summary>
    public async Task<IEnumerable<RuntimePluginInstance>> GetPluginsByTypeAsync(string type)
    {
        return await Task.FromResult(_loadedPlugins.Values
            .Where(p => p.Type.Equals(type, StringComparison.OrdinalIgnoreCase) && p.IsEnabled));
    }

    /// <summary>
    /// Invoke any method on a plugin using reflection
    /// </summary>
    public async Task<object?> InvokePluginMethodAsync(string pluginName, string methodName, params object[] parameters)
    {
        if (!_loadedPlugins.TryGetValue(pluginName, out var plugin))
        {
            _logger.LogWarning("Plugin not found: {PluginName}", pluginName);
            return null;
        }

        try
        {
            return await plugin.InvokeMethodAsync(methodName, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking method {MethodName} on plugin: {PluginName}", methodName, pluginName);
            return null;
        }
    }

    /// <summary>
    /// Load all plugins from the plugins directory
    /// </summary>
    public async Task<int> LoadAllPluginsAsync()
    {
        var loadedCount = 0;

        try
        {
            if (!Directory.Exists(_pluginsDirectory))
            {
                _logger.LogWarning("Plugins directory not found: {Directory}", _pluginsDirectory);
                return 0;
            }

            var pluginFiles = Directory.GetFiles(_pluginsDirectory, "*.dll", SearchOption.AllDirectories);

            foreach (var pluginFile in pluginFiles)
            {
                try
                {
                    var success = await LoadPluginAsync(pluginFile);
                    if (success)
                    {
                        loadedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading plugin: {PluginFile}", pluginFile);
                }
            }

            _logger.LogInformation("Loaded {LoadedCount} plugins from {TotalFiles} files", loadedCount, pluginFiles.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading plugins from directory: {Directory}", _pluginsDirectory);
        }

        return loadedCount;
    }
}

/// <summary>
/// Plugin manifest structure for runtime loading
/// </summary>
public class PluginManifest
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public string AssemblyName { get; set; } = string.Empty;
    public string EntryPoint { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int Priority { get; set; } = 50;
    public string[]? SupportedFeatures { get; set; }
    public Dictionary<string, PluginConfigurationManifest>? Configuration { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class PluginConfigurationManifest
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsSecret { get; set; }
    public object? DefaultValue { get; set; }
}


