using System.Reflection;
using System.Text.Json;
using PluginContract.Models;
using PluginContract.Enums;

namespace NotifyMasterApi.Services;

/// <summary>
/// Represents a runtime-loaded plugin instance with no concrete type references
/// </summary>
public class RuntimePluginInstance : IDisposable
{
    private readonly PluginLoadContext _loadContext;
    private readonly object _pluginInstance;
    private readonly Type _pluginType;
    private readonly PluginManifest _manifest;
    private bool _disposed = false;

    public string Name => _manifest.Name;
    public string Version => _manifest.Version;
    public string Type => _manifest.Type;
    public string Provider => _manifest.Provider;
    public bool IsEnabled { get; set; }
    public PluginManifest Manifest => _manifest;
    public Assembly Assembly { get; }

    public RuntimePluginInstance(
        PluginLoadContext loadContext, 
        object pluginInstance, 
        Type pluginType, 
        PluginManifest manifest,
        Assembly assembly)
    {
        _loadContext = loadContext;
        _pluginInstance = pluginInstance;
        _pluginType = pluginType;
        _manifest = manifest;
        Assembly = assembly;
        IsEnabled = manifest.IsEnabled;
    }

    /// <summary>
    /// Initialize the plugin using reflection
    /// </summary>
    public async Task<bool> InitializeAsync(IConfiguration configuration)
    {
        try
        {
            var initMethod = _pluginType.GetMethod("InitializeAsync");
            if (initMethod != null)
            {
                var result = initMethod.Invoke(_pluginInstance, new object[] { configuration });
                if (result is Task task)
                {
                    await task;
                    return true;
                }
            }
            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// Validate plugin configuration using reflection
    /// </summary>
    public async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        try
        {
            var validateMethod = _pluginType.GetMethod("ValidateConfigurationAsync");
            if (validateMethod != null)
            {
                var result = validateMethod.Invoke(_pluginInstance, new object[] { configuration });
                if (result is Task<bool> task)
                {
                    return await task;
                }
            }
            return true; // Default to valid if no validation method
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// Send notification using reflection (works for any notification type)
    /// </summary>
    public async Task<object?> SendNotificationAsync(object request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Try different method names based on plugin type
            var methodNames = new[]
            {
                "SendAsync",
                "SendSmsAsync", 
                "SendEmailAsync",
                "SendPushAsync"
            };

            foreach (var methodName in methodNames)
            {
                var method = _pluginType.GetMethod(methodName);
                if (method != null)
                {
                    var parameters = method.GetParameters();
                    object[] args;

                    if (parameters.Length == 2 && parameters[1].ParameterType == typeof(CancellationToken))
                    {
                        args = new object[] { request, cancellationToken };
                    }
                    else
                    {
                        args = new object[] { request };
                    }

                    var result = method.Invoke(_pluginInstance, args);
                    if (result is Task task)
                    {
                        await task;
                        // Get the result from the task
                        var resultProperty = task.GetType().GetProperty("Result");
                        return resultProperty?.GetValue(task);
                    }
                    return result;
                }
            }

            return null;
        }
        catch (Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// Perform health check using reflection
    /// </summary>
    public async Task<bool> HealthCheckAsync()
    {
        try
        {
            var healthMethod = _pluginType.GetMethod("HealthCheckAsync");
            if (healthMethod != null)
            {
                var result = healthMethod.Invoke(_pluginInstance, Array.Empty<object>());
                if (result is Task<bool> task)
                {
                    return await task;
                }
            }
            return true; // Default to healthy if no health check method
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// Get plugin information using reflection
    /// </summary>
    public PluginInfo GetPluginInfo()
    {
        // Convert string type to PluginType enum
        var pluginType = _manifest.Type.ToLowerInvariant() switch
        {
            "sms" => PluginContract.Enums.PluginType.Sms,
            "email" => PluginContract.Enums.PluginType.Email,
            "push" => PluginContract.Enums.PluginType.PushNotification,
            _ => PluginContract.Enums.PluginType.Sms // Default
        };

        return new PluginInfo(
            Name: _manifest.Name,
            Version: _manifest.Version,
            Description: _manifest.Description,
            Type: pluginType,
            Author: _manifest.Author,
            IsEnabled: IsEnabled
        );
    }

    /// <summary>
    /// Invoke any method on the plugin using reflection
    /// </summary>
    public async Task<object?> InvokeMethodAsync(string methodName, params object[] parameters)
    {
        try
        {
            var method = _pluginType.GetMethod(methodName);
            if (method != null)
            {
                var result = method.Invoke(_pluginInstance, parameters);
                if (result is Task task)
                {
                    await task;
                    var resultProperty = task.GetType().GetProperty("Result");
                    return resultProperty?.GetValue(task);
                }
                return result;
            }
            return null;
        }
        catch (Exception)
        {
            return null;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            // Dispose plugin if it implements IDisposable
            if (_pluginInstance is IDisposable disposablePlugin)
            {
                disposablePlugin.Dispose();
            }

            // Unload the plugin context
            _loadContext?.Unload();
            _disposed = true;
        }
    }
}


