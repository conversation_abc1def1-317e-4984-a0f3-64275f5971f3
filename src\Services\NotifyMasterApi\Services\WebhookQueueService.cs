using Hangfire;
using Microsoft.Extensions.Logging;
using NotifyMasterApi.Infrastructure;
using System.Text;
using System.Text.Json;

namespace NotifyMasterApi.Services;

public interface IWebhookQueueService
{
    Task<string> QueueWebhookAsync(OutboundWebhookJob webhook);
    Task<List<OutboundWebhookJob>> GetWebhooksAsync(string? tenantId = null, string? status = null);
    Task<OutboundWebhookJob?> GetWebhookAsync(string webhookId);
    Task<bool> RetryWebhookAsync(string webhookId);
    Task<bool> CancelWebhookAsync(string webhookId);
    Task<List<WebhookRetryResult>> GetWebhookHistoryAsync(string webhookId);
}

public class WebhookQueueService : IWebhookQueueService
{
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<WebhookQueueService> _logger;
    private readonly Dictionary<string, OutboundWebhookJob> _webhookStore = new();
    private readonly Dictionary<string, List<WebhookRetryResult>> _webhookHistory = new();

    public WebhookQueueService(
        IBackgroundJobClient backgroundJobClient,
        IHttpClientFactory httpClientFactory,
        ILogger<WebhookQueueService> logger)
    {
        _backgroundJobClient = backgroundJobClient;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    public async Task<string> QueueWebhookAsync(OutboundWebhookJob webhook)
    {
        webhook.Id = Guid.NewGuid().ToString();
        webhook.Status = "Pending";
        webhook.CreatedAt = DateTime.UtcNow;

        _webhookStore[webhook.Id] = webhook;
        _webhookHistory[webhook.Id] = new List<WebhookRetryResult>();

        // Queue the webhook for immediate processing
        var jobId = _backgroundJobClient.Enqueue(() => ProcessWebhookAsync(webhook.Id));
        
        _logger.LogInformation("Queued webhook {WebhookId} for {Url}", webhook.Id, webhook.Url);
        
        return webhook.Id;
    }

    public async Task<List<OutboundWebhookJob>> GetWebhooksAsync(string? tenantId = null, string? status = null)
    {
        var webhooks = _webhookStore.Values.AsEnumerable();

        if (!string.IsNullOrEmpty(tenantId))
        {
            webhooks = webhooks.Where(w => w.TenantId == tenantId);
        }

        if (!string.IsNullOrEmpty(status))
        {
            webhooks = webhooks.Where(w => w.Status.Equals(status, StringComparison.OrdinalIgnoreCase));
        }

        return webhooks.OrderByDescending(w => w.CreatedAt).ToList();
    }

    public async Task<OutboundWebhookJob?> GetWebhookAsync(string webhookId)
    {
        _webhookStore.TryGetValue(webhookId, out var webhook);
        return webhook;
    }

    public async Task<bool> RetryWebhookAsync(string webhookId)
    {
        if (!_webhookStore.TryGetValue(webhookId, out var webhook))
        {
            return false;
        }

        if (webhook.Status == "Completed")
        {
            return false; // Already completed
        }

        webhook.Status = "Pending";
        webhook.NextRetryAt = null;
        
        var jobId = _backgroundJobClient.Enqueue(() => ProcessWebhookAsync(webhookId));
        
        _logger.LogInformation("Manually retrying webhook {WebhookId}", webhookId);
        
        return true;
    }

    public async Task<bool> CancelWebhookAsync(string webhookId)
    {
        if (!_webhookStore.TryGetValue(webhookId, out var webhook))
        {
            return false;
        }

        webhook.Status = "Cancelled";
        
        _logger.LogInformation("Cancelled webhook {WebhookId}", webhookId);
        
        return true;
    }

    public async Task<List<WebhookRetryResult>> GetWebhookHistoryAsync(string webhookId)
    {
        _webhookHistory.TryGetValue(webhookId, out var history);
        return history ?? new List<WebhookRetryResult>();
    }

    [AutomaticRetry(Attempts = 0)] // We handle retries manually
    public async Task ProcessWebhookAsync(string webhookId)
    {
        if (!_webhookStore.TryGetValue(webhookId, out var webhook))
        {
            _logger.LogWarning("Webhook {WebhookId} not found", webhookId);
            return;
        }

        if (webhook.Status == "Cancelled" || webhook.Status == "Completed")
        {
            return;
        }

        webhook.Status = "Processing";
        
        var result = await ExecuteWebhookAsync(webhook);
        
        if (!_webhookHistory.ContainsKey(webhookId))
        {
            _webhookHistory[webhookId] = new List<WebhookRetryResult>();
        }
        _webhookHistory[webhookId].Add(result);

        if (result.Success)
        {
            webhook.Status = "Completed";
            _logger.LogInformation("Webhook {WebhookId} completed successfully", webhookId);
        }
        else
        {
            webhook.RetryCount++;
            webhook.LastError = result.Error;

            if (webhook.RetryCount >= webhook.MaxRetries)
            {
                webhook.Status = "Failed";
                _logger.LogError("Webhook {WebhookId} failed permanently after {RetryCount} attempts", 
                    webhookId, webhook.RetryCount);
            }
            else
            {
                webhook.Status = "Pending";
                var delay = CalculateRetryDelay(webhook.RetryCount);
                webhook.NextRetryAt = DateTime.UtcNow.Add(delay);
                
                // Schedule retry
                _backgroundJobClient.Schedule(() => ProcessWebhookAsync(webhookId), delay);
                
                _logger.LogWarning("Webhook {WebhookId} failed, scheduling retry {RetryCount}/{MaxRetries} in {Delay}", 
                    webhookId, webhook.RetryCount, webhook.MaxRetries, delay);
            }
        }
    }

    private async Task<WebhookRetryResult> ExecuteWebhookAsync(OutboundWebhookJob webhook)
    {
        var result = new WebhookRetryResult();
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            var request = new HttpRequestMessage(
                new HttpMethod(webhook.Method), 
                webhook.Url);

            // Add headers
            foreach (var header in webhook.Headers)
            {
                request.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            // Add payload
            if (!string.IsNullOrEmpty(webhook.Payload))
            {
                request.Content = new StringContent(webhook.Payload, Encoding.UTF8, "application/json");
            }

            var response = await httpClient.SendAsync(request);
            
            result.StatusCode = (int)response.StatusCode;
            result.Response = await response.Content.ReadAsStringAsync();
            result.Success = response.IsSuccessStatusCode;
            
            if (!result.Success)
            {
                result.Error = $"HTTP {result.StatusCode}: {result.Response}";
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Error = ex.Message;
            result.StatusCode = 0;
        }
        finally
        {
            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;
        }

        return result;
    }

    private TimeSpan CalculateRetryDelay(int retryCount)
    {
        // Exponential backoff: 30s, 1m, 2m, 4m, 8m, max 15m
        var baseDelay = TimeSpan.FromSeconds(30);
        var exponentialDelay = TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, retryCount - 1));
        var maxDelay = TimeSpan.FromMinutes(15);
        
        return exponentialDelay > maxDelay ? maxDelay : exponentialDelay;
    }
}

public interface IEventStreamService
{
    Task PublishEventAsync(SystemEvent systemEvent);
    Task<List<SystemEvent>> GetEventsAsync(string? tenantId = null, string? type = null, DateTime? since = null);
    Task SubscribeToEventsAsync(string connectionId, string? tenantId = null);
    Task UnsubscribeFromEventsAsync(string connectionId);
}

public class EventStreamService : IEventStreamService
{
    private readonly ILogger<EventStreamService> _logger;
    private readonly List<SystemEvent> _eventStore = new();
    private readonly Dictionary<string, string?> _subscriptions = new(); // connectionId -> tenantId

    public EventStreamService(ILogger<EventStreamService> logger)
    {
        _logger = logger;
    }

    public async Task PublishEventAsync(SystemEvent systemEvent)
    {
        systemEvent.Id = Guid.NewGuid().ToString();
        systemEvent.Timestamp = DateTime.UtcNow;
        
        _eventStore.Add(systemEvent);
        
        // Keep only last 10000 events in memory
        if (_eventStore.Count > 10000)
        {
            _eventStore.RemoveRange(0, _eventStore.Count - 10000);
        }

        _logger.LogDebug("Published event {EventId} of type {Type} for tenant {TenantId}", 
            systemEvent.Id, systemEvent.Type, systemEvent.TenantId);

        // In a real implementation, this would push to SignalR clients
        await NotifySubscribersAsync(systemEvent);
    }

    public async Task<List<SystemEvent>> GetEventsAsync(string? tenantId = null, string? type = null, DateTime? since = null)
    {
        var events = _eventStore.AsEnumerable();

        if (!string.IsNullOrEmpty(tenantId))
        {
            events = events.Where(e => e.TenantId == tenantId);
        }

        if (!string.IsNullOrEmpty(type))
        {
            events = events.Where(e => e.Type.Equals(type, StringComparison.OrdinalIgnoreCase));
        }

        if (since.HasValue)
        {
            events = events.Where(e => e.Timestamp >= since.Value);
        }

        return events.OrderByDescending(e => e.Timestamp).Take(1000).ToList();
    }

    public async Task SubscribeToEventsAsync(string connectionId, string? tenantId = null)
    {
        _subscriptions[connectionId] = tenantId;
        _logger.LogDebug("Connection {ConnectionId} subscribed to events for tenant {TenantId}", 
            connectionId, tenantId ?? "all");
    }

    public async Task UnsubscribeFromEventsAsync(string connectionId)
    {
        _subscriptions.Remove(connectionId);
        _logger.LogDebug("Connection {ConnectionId} unsubscribed from events", connectionId);
    }

    private async Task NotifySubscribersAsync(SystemEvent systemEvent)
    {
        // In a real implementation, this would use SignalR to notify connected clients
        var relevantSubscriptions = _subscriptions.Where(s => 
            s.Value == null || s.Value == systemEvent.TenantId);

        foreach (var subscription in relevantSubscriptions)
        {
            // SignalR notification would go here
            _logger.LogDebug("Would notify connection {ConnectionId} of event {EventId}", 
                subscription.Key, systemEvent.Id);
        }
    }
}
