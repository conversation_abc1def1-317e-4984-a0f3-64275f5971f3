{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"NotifyMasterApi/1.0.0": {"dependencies": {"AWSSDK.S3": "3.7.402.8", "Azure.Storage.Blobs": "12.21.2", "EmailContract": "1.0.0", "EmailService.Library": "1.0.0", "FastEndpoints": "5.30.0", "Hangfire.AspNetCore": "1.8.14", "Hangfire.Core": "1.8.14", "Hangfire.InMemory": "0.10.4", "Hangfire.SqlServer": "1.8.14", "Microsoft.AspNetCore.OpenApi": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.InMemory": "9.0.1", "Microsoft.Extensions.Caching.StackExchangeRedis": "9.0.0", "NotificationContract": "1.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "PluginContract": "1.0.0", "PluginCore": "1.0.0", "PushNotificationContract": "1.0.0", "PushNotificationService.Library": "1.0.0", "Scalar.AspNetCore": "1.2.42", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "SmsContract": "1.0.0", "SmsService.Library": "1.0.0"}, "runtime": {"NotifyMasterApi.dll": {}}}, "AWSSDK.Core/3.7.400.17": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.400.17"}}}, "AWSSDK.S3/3.7.402.8": {"dependencies": {"AWSSDK.Core": "3.7.400.17"}, "runtime": {"lib/net8.0/AWSSDK.S3.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.402.8"}}}, "Azure.Core/1.41.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.0.0", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.41.0.0", "fileVersion": "1.4100.24.36109"}}}, "Azure.Storage.Blobs/12.21.2": {"dependencies": {"Azure.Storage.Common": "12.20.1"}, "runtime": {"lib/net6.0/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.21.2.0", "fileVersion": "12.2100.224.40807"}}}, "Azure.Storage.Common/12.20.1": {"dependencies": {"Azure.Core": "1.41.0", "System.IO.Hashing": "6.0.0"}, "runtime": {"lib/net6.0/Azure.Storage.Common.dll": {"assemblyVersion": "12.20.1.0", "fileVersion": "12.2000.124.37501"}}}, "BouncyCastle.Cryptography/2.5.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.1.28965"}}}, "FastEndpoints/5.30.0": {"dependencies": {"FastEndpoints.Attributes": "5.30.0", "FastEndpoints.Messaging.Core": "5.30.0", "FluentValidation": "11.10.0"}, "runtime": {"lib/net9.0/FastEndpoints.dll": {"assemblyVersion": "5.30.0.0", "fileVersion": "5.30.0.0"}}}, "FastEndpoints.Attributes/5.30.0": {"runtime": {"lib/netstandard2.0/FastEndpoints.Attributes.dll": {"assemblyVersion": "5.30.0.0", "fileVersion": "5.30.0.0"}}}, "FastEndpoints.Messaging.Core/5.30.0": {"runtime": {"lib/netstandard2.1/FastEndpoints.Messaging.Core.dll": {"assemblyVersion": "5.30.0.0", "fileVersion": "5.30.0.0"}}}, "FirebaseAdmin/3.2.0": {"dependencies": {"Google.Api.Gax.Rest": "4.8.0", "Google.Apis.Auth": "1.68.0"}, "runtime": {"lib/net6.0/FirebaseAdmin.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "FluentValidation/11.10.0": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.10.0.0"}}}, "Google.Api.Gax/4.8.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Api.Gax.Rest/4.8.0": {"dependencies": {"Google.Api.Gax": "4.8.0", "Google.Apis.Auth": "1.68.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Apis/1.68.0": {"dependencies": {"Google.Apis.Core": "1.68.0"}, "runtime": {"lib/net6.0/Google.Apis.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.Auth/1.68.0": {"dependencies": {"Google.Apis": "1.68.0", "Google.Apis.Core": "1.68.0", "System.Management": "7.0.2"}, "runtime": {"lib/net6.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.Core/1.68.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Google.Apis.Core.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Hangfire.AspNetCore/1.8.14": {"dependencies": {"Hangfire.NetCore": "1.8.14"}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"assemblyVersion": "1.8.14.0", "fileVersion": "1.8.14.0"}}}, "Hangfire.Core/1.8.14": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"assemblyVersion": "1.8.14.0", "fileVersion": "1.8.14.0"}}}, "Hangfire.InMemory/0.10.4": {"dependencies": {"Hangfire.Core": "1.8.14"}, "runtime": {"lib/netstandard2.0/Hangfire.InMemory.dll": {"assemblyVersion": "0.10.4.0", "fileVersion": "0.10.4.0"}}}, "Hangfire.NetCore/1.8.14": {"dependencies": {"Hangfire.Core": "1.8.14"}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"assemblyVersion": "1.8.14.0", "fileVersion": "1.8.14.0"}}}, "Hangfire.SqlServer/1.8.14": {"dependencies": {"Hangfire.Core": "1.8.14"}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"assemblyVersion": "1.8.14.0", "fileVersion": "1.8.14.0"}}}, "MailKit/4.13.0": {"dependencies": {"MimeKit": "4.13.0"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "4.13.0.0", "fileVersion": "4.13.0.0"}}}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"dependencies": {"Microsoft.OpenApi": "1.6.17"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.EntityFrameworkCore/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.1.0", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.1": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.1.0", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.InMemory/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {"assemblyVersion": "9.0.1.0", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.1.0", "fileVersion": "9.0.124.61002"}}}, "Microsoft.Extensions.Caching.StackExchangeRedis/9.0.0": {"dependencies": {"StackExchange.Redis": "2.7.27"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.StackExchangeRedis.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.OpenApi/1.6.17": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.17.0", "fileVersion": "1.6.17.0"}}}, "MimeKit/4.13.0": {"dependencies": {"BouncyCastle.Cryptography": "2.5.1"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "4.13.0.0", "fileVersion": "4.13.0.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Npgsql/9.0.3": {"runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Npgsql": "9.0.3"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Scalar.AspNetCore/1.2.42": {"runtime": {"lib/net9.0/Scalar.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Serilog/4.2.0": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/9.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/9.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/9.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.7.27": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.7.27.49176"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/7.0.0": {"runtime": {"lib/net7.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IO.Hashing/6.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Management/7.0.2": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"lib/net7.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "System.Memory.Data/1.0.2": {"runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Reflection.MetadataLoadContext/9.0.0": {"runtime": {"lib/net9.0/System.Reflection.MetadataLoadContext.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "EmailContract/1.0.0": {"dependencies": {"MimeKit": "4.13.0"}, "runtime": {"EmailContract.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EmailService.Library/1.0.0": {"dependencies": {"EmailContract": "1.0.0", "MailKit": "4.13.0", "NotificationContract": "1.0.0"}, "runtime": {"EmailService.Library.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NotificationContract/1.0.0": {"dependencies": {"EmailContract": "1.0.0", "PushNotificationContract": "1.0.0", "SmsContract": "1.0.0"}, "runtime": {"NotificationContract.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PluginContract/1.0.0": {"runtime": {"PluginContract.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PluginCore/1.0.0": {"dependencies": {"EmailContract": "1.0.0", "PluginContract": "1.0.0", "PushNotificationContract": "1.0.0", "SmsContract": "1.0.0", "System.Reflection.MetadataLoadContext": "9.0.0"}, "runtime": {"PluginCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PushNotificationContract/1.0.0": {"dependencies": {"FirebaseAdmin": "3.2.0"}, "runtime": {"PushNotificationContract.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PushNotificationService.Library/1.0.0": {"dependencies": {"FirebaseAdmin": "3.2.0", "NotificationContract": "1.0.0", "PushNotificationContract": "1.0.0"}, "runtime": {"PushNotificationService.Library.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SmsContract/1.0.0": {"runtime": {"SmsContract.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SmsService.Library/1.0.0": {"dependencies": {"NotificationContract": "1.0.0", "SmsContract": "1.0.0"}, "runtime": {"SmsService.Library.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"NotifyMasterApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AWSSDK.Core/3.7.400.17": {"type": "package", "serviceable": true, "sha512": "sha512-8u3Or4sYGCzoc0RAjIl79SKZeO/Bk8RRaD2EPdAhXVJEothuDXkGWjVe+K4NExRczQJzOQ9H8m8Dn89Wn07CHw==", "path": "awssdk.core/3.7.400.17", "hashPath": "awssdk.core.3.7.400.17.nupkg.sha512"}, "AWSSDK.S3/3.7.402.8": {"type": "package", "serviceable": true, "sha512": "sha512-8xU65wXvTHvbaGKD97Jnj7MKZzSAx0+o6rlVMLV61HznAwx79+OyfxqUwEDDXErIdhyz7mPqjba+C33+R33+bg==", "path": "awssdk.s3/3.7.402.8", "hashPath": "awssdk.s3.3.7.402.8.nupkg.sha512"}, "Azure.Core/1.41.0": {"type": "package", "serviceable": true, "sha512": "sha512-7OO8rPCVSvXj2IQET3NkRf8hU2ZDCCvCIUhlrE089qkLNpNfWufJnBwHRKLAOWF3bhKBGJS/9hPBgjJ8kupUIg==", "path": "azure.core/1.41.0", "hashPath": "azure.core.1.41.0.nupkg.sha512"}, "Azure.Storage.Blobs/12.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-2J+sMgNbj2DJ+ydRSqYYADDd2AajFLfPzLGxASOxcoGx4iVxyF6jscHw2IY+8QyMPWA09wN3lCtYJ5S4zIsJkA==", "path": "azure.storage.blobs/12.21.2", "hashPath": "azure.storage.blobs.12.21.2.nupkg.sha512"}, "Azure.Storage.Common/12.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-KKBFnc4WZ6m9HgsKgwfO1cIxd154b8cAnP3uWhuelvFkzxqBXQQgIsHF0n3DYBG2AoTJCZDXwJpKuVC7CsKJWg==", "path": "azure.storage.common/12.20.1", "hashPath": "azure.storage.common.12.20.1.nupkg.sha512"}, "BouncyCastle.Cryptography/2.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-zy8TMeTP+1FH2NrLaNZtdRbBdq7u5MI+NFZQOBSM69u5RFkciinwzV2eveY6Kjf5MzgsYvvl6kTStsj3JrXqkg==", "path": "bouncycastle.cryptography/2.5.1", "hashPath": "bouncycastle.cryptography.2.5.1.nupkg.sha512"}, "FastEndpoints/5.30.0": {"type": "package", "serviceable": true, "sha512": "sha512-jL1tSR3J8WXvk4xzr/bJw8XfWct1Nw+/Ah9gR3YMabFngvJmAxl6GwI9VixGJr0yayp2dDyt4myDYfJ8XMRd0w==", "path": "fastendpoints/5.30.0", "hashPath": "fastendpoints.5.30.0.nupkg.sha512"}, "FastEndpoints.Attributes/5.30.0": {"type": "package", "serviceable": true, "sha512": "sha512-0FB/0mVDOHoQKbpZXUiJnh+w9apoh676IYeViHqhCq3GEwx9T9phrIawBXUNyaMyM1u2/hmtYPzPr2e+Qc09Rg==", "path": "fastendpoints.attributes/5.30.0", "hashPath": "fastendpoints.attributes.5.30.0.nupkg.sha512"}, "FastEndpoints.Messaging.Core/5.30.0": {"type": "package", "serviceable": true, "sha512": "sha512-Cvx/61JSG6WaNbdS7ypImTJygC7p0lftpg1IzE+vs5BLhkSIxYUfYyPck/P/Mw4Re2TcrWor+Ly5cbRTFcZP6g==", "path": "fastendpoints.messaging.core/5.30.0", "hashPath": "fastendpoints.messaging.core.5.30.0.nupkg.sha512"}, "FirebaseAdmin/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-689HWz59pmUQVJY3YJd7rsevICX8KL2qQX23XyHIp2+Qek9IScgL6T/AnyHF6WMirXll2tULRe+LGOItj7kF6A==", "path": "firebaseadmin/3.2.0", "hashPath": "firebaseadmin.3.2.0.nupkg.sha512"}, "FluentValidation/11.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-qsJGSJDdZ8qiG+lVJ70PZfJHcEdq8UQZ/tZDXoj78/iHKG6lVKtMJsD11zyyv/IPc7rwqGqnFoFLTNzpo3IPYg==", "path": "fluentvalidation/11.10.0", "hashPath": "fluentvalidation.11.10.0.nupkg.sha512"}, "Google.Api.Gax/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlV8Jq/G5CQAA3PwYAuKGjfzGOP7AvjhREnE6vgZlzxREGYchHudZWa2PWSqFJL+MBtz9YgitLpRogANN3CVvg==", "path": "google.api.gax/4.8.0", "hashPath": "google.api.gax.4.8.0.nupkg.sha512"}, "Google.Api.Gax.Rest/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaA5LZ2VvGj/wwIzRB68swr7khi2kWNgqWvsB0fYtScIAl3kGkGtqiBcx63H1YLeKr5xau1866bFjTeReH6FSQ==", "path": "google.api.gax.rest/4.8.0", "hashPath": "google.api.gax.rest.4.8.0.nupkg.sha512"}, "Google.Apis/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-s2MymhdpH+ybZNBeZ2J5uFgFHApBp+QXf9FjZSdM1lk/vx5VqIknJwnaWiuAzXxPrLEkesX0Q+UsiWn39yZ9zw==", "path": "google.apis/1.68.0", "hashPath": "google.apis.1.68.0.nupkg.sha512"}, "Google.Apis.Auth/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-hFx8Qz5bZ4w0hpnn4tSmZaaFpjAMsgVElZ+ZgVLUZ2r9i+AKcoVgwiNfv1pruNS5cCvpXqhKECbruBCfRezPHA==", "path": "google.apis.auth/1.68.0", "hashPath": "google.apis.auth.1.68.0.nupkg.sha512"}, "Google.Apis.Core/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-pAqwa6pfu53UXCR2b7A/PAPXeuVg6L1OFw38WckN27NU2+mf+KTjoEg2YGv/f0UyKxzz7DxF1urOTKg/6dTP9g==", "path": "google.apis.core/1.68.0", "hashPath": "google.apis.core.1.68.0.nupkg.sha512"}, "Hangfire.AspNetCore/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-aTPzKN/g9SW30QB1SLTOMuckKqTVL1YAhtEpWll4LPbPTgyeBHJTChdRhrN127fj+mqJ6P4P7+HJBhNYUFTfNw==", "path": "hangfire.aspnetcore/1.8.14", "hashPath": "hangfire.aspnetcore.1.8.14.nupkg.sha512"}, "Hangfire.Core/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-tj/+J8/UdaxydFX6VQr5IEyBtVbAOvkQ8X8tIQKwY9zlpmK83hP4iHEQQQ26zzGUpcE1HlPc6PBUv0NgUDXS3A==", "path": "hangfire.core/1.8.14", "hashPath": "hangfire.core.1.8.14.nupkg.sha512"}, "Hangfire.InMemory/0.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-oYigjbOAzwjOsNkmU+CaybH/1mwSQevGrUkN/9UvrSrYYrkU2bdYnJbqpgeCPYGY3ZMtHNEoaF0GdC9s1sAkSQ==", "path": "hangfire.inmemory/0.10.4", "hashPath": "hangfire.inmemory.0.10.4.nupkg.sha512"}, "Hangfire.NetCore/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-fBLdsxWYFdrQuenvVHEj/z8nOXoOTqpWIl4qYoinBAUCVmp4qlxfFsY3Aq3VVbwket0wBH472aG2LAmYm6hjxw==", "path": "hangfire.netcore/1.8.14", "hashPath": "hangfire.netcore.1.8.14.nupkg.sha512"}, "Hangfire.SqlServer/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-OrsxbJD0UYanIk4E1oqwffZSWRfltYXJa89Icn+fcWmOLGyWTiAg9j5UX4MoS2RaS3WyZG8xbZzbhoRqnujo8g==", "path": "hangfire.sqlserver/1.8.14", "hashPath": "hangfire.sqlserver.1.8.14.nupkg.sha512"}, "MailKit/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsepEHKkaQvbAuBizlhz93yc0ihJWzVCfoerfnpCeqiKLeS6gsTKInYy3/U2wqgkGE62TKs5OKS1a90pyc+j4g==", "path": "mailkit/4.13.0", "hashPath": "mailkit.4.13.0.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-MOJ4DG1xd3NlWMYh+JdGNT9uvBtEk1XQU/FQlpNZFlAzM8t0oB5IimvnGlnK7jmyY4vQagLPB1xw1HjJ8CHrZg==", "path": "microsoft.aspnetcore.openapi/9.0.6", "hashPath": "microsoft.aspnetcore.openapi.9.0.6.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E25w4XugXNykTr5Y/sLDGaQ4lf67n9aXVPvsdGsIZjtuLmbvb9AoYP8D50CDejY8Ro4D9GK2kNHz5lWHqSK+wg==", "path": "microsoft.entityframeworkcore/9.0.1", "hashPath": "microsoft.entityframeworkcore.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qy+taGVLUs82zeWfc32hgGL8Z02ZqAneYvqZiiXbxF4g4PBUcPRuxHM9K20USmpeJbn4/fz40GkCbyyCy5ojOA==", "path": "microsoft.entityframeworkcore.abstractions/9.0.1", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XTuTax42m89JET3rgjWphx11pGXD77dc9kAO8B4l9Ze9NAJnw7qDL/cqmaGiHEAxL/8f0PJ+zY4FII0v/nXlmg==", "path": "microsoft.entityframeworkcore.inmemory/9.0.1", "hashPath": "microsoft.entityframeworkcore.inmemory.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7Iu0h4oevRvH4IwPzmxuIJGYRt55TapoREGlluk75KCO7lenN0+QnzCl6cQDY48uDoxAUpJbpK2xW7o8Ix69dw==", "path": "microsoft.entityframeworkcore.relational/9.0.1", "hashPath": "microsoft.entityframeworkcore.relational.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.StackExchangeRedis/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sxxJa+S6++s+J7tipY1DjdhAIO279hAOItCMKnpeEOXrU4SNqcjKNjemssgSJ1uMN5rgbuv52CzMf7UWnLYgiw==", "path": "microsoft.extensions.caching.stackexchangeredis/9.0.0", "hashPath": "microsoft.extensions.caching.stackexchangeredis.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.17": {"type": "package", "serviceable": true, "sha512": "sha512-Le+kehlmrlQfuDFUt1zZ2dVwrhFQtKREdKBo+rexOwaCoYP0/qpgT9tLxCsZjsgR5Itk1UKPcbgO+FyaNid/bA==", "path": "microsoft.openapi/1.6.17", "hashPath": "microsoft.openapi.1.6.17.nupkg.sha512"}, "MimeKit/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-oa4JuhAzJydHnPCc/XWeyBUGd3uiVyWW0NXqOVgkXEHjbHlPVBssklK3mpw9sokjzAaBGdj0bceFsr+NXvAukA==", "path": "mimekit/4.13.0", "hashPath": "mimekit.4.13.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Scalar.AspNetCore/1.2.42": {"type": "package", "serviceable": true, "sha512": "sha512-knBM37CHGmB/S8hksuWH59/qYk3bhhSUTKPaCc0IgbrHO6hSfRVP8py1cuvq88klpbHmSljRgQ5xXaBlrcdZ2w==", "path": "scalar.aspnetcore/1.2.42", "hashPath": "scalar.aspnetcore.1.2.42.nupkg.sha512"}, "Serilog/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "path": "serilog/4.2.0", "hashPath": "serilog.4.2.0.nupkg.sha512"}, "Serilog.AspNetCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "path": "serilog.aspnetcore/9.0.0", "hashPath": "serilog.aspnetcore.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "path": "serilog.extensions.hosting/9.0.0", "hashPath": "serilog.extensions.hosting.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "path": "serilog.extensions.logging/9.0.0", "hashPath": "serilog.extensions.logging.9.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "path": "serilog.formatting.compact/3.0.0", "hashPath": "serilog.formatting.compact.3.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "path": "serilog.settings.configuration/9.0.0", "hashPath": "serilog.settings.configuration.9.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "StackExchange.Redis/2.7.27": {"type": "package", "serviceable": true, "sha512": "sha512-Uqc2OQHglqj9/FfGQ6RkKFkZfHySfZlfmbCl+hc+u2I/IqunfelQ7QJi7ZhvAJxUtu80pildVX6NPLdDaUffOw==", "path": "stackexchange.redis/2.7.27", "hashPath": "stackexchange.redis.2.7.27.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.IO.Hashing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "path": "system.io.hashing/6.0.0", "hashPath": "system.io.hashing.6.0.0.nupkg.sha512"}, "System.Management/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "path": "system.management/7.0.2", "hashPath": "system.management.7.0.2.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Reflection.MetadataLoadContext/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nGdCUVhEQ9/CWYqgaibYEDwIJjokgIinQhCnpmtZfSXdMS6ysLZ8p9xvcJ8VPx6Xpv5OsLIUrho4B9FN+VV/tw==", "path": "system.reflection.metadataloadcontext/9.0.0", "hashPath": "system.reflection.metadataloadcontext.9.0.0.nupkg.sha512"}, "EmailContract/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EmailService.Library/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "NotificationContract/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PluginContract/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PluginCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PushNotificationContract/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PushNotificationService.Library/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SmsContract/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SmsService.Library/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}