# Stoplight Configuration for NotificationService API
# This file configures Stoplight Studio for API documentation

# Project configuration
project:
  name: "NotificationService API"
  description: "A comprehensive notification service supporting Email, SMS, and Push notifications with plugin architecture"
  version: "1.0.0"

# API specification source
spec:
  # The OpenAPI spec will be generated from the running service
  url: "http://localhost:5000/openapi.json"
  # Alternative: local file path for static spec
  # file: "./openapi.json"

# Documentation settings
docs:
  # Enable interactive documentation
  interactive: true
  
  # Show request/response examples
  examples: true
  
  # Show code samples in multiple languages
  codeSamples:
    - lang: "curl"
    - lang: "javascript"
    - lang: "csharp"
    - lang: "python"
    - lang: "java"

# Styling and branding
theme:
  # Primary brand color
  primaryColor: "#2563eb"
  
  # Logo URL (optional)
  # logo: "https://your-domain.com/logo.png"
  
  # Favicon URL (optional)
  # favicon: "https://your-domain.com/favicon.ico"

# Navigation and organization
navigation:
  # Group endpoints by tags
  groupByTags: true
  
  # Show operation summaries in navigation
  showSummary: true

# Server configuration for testing
servers:
  - url: "http://localhost:5000"
    description: "Development server"
  - url: "https://api.notificationservice.com"
    description: "Production server"

# Mock server settings (for testing)
mock:
  enabled: true
  # Use examples from the OpenAPI spec
  useExamples: true

# Validation settings
validation:
  # Enable OpenAPI spec validation
  enabled: true
  
  # Validation rules
  rules:
    - "no-unused-components"
    - "operation-description"
    - "operation-summary"
    - "parameter-description"
    - "response-description"

# Publishing settings
publish:
  # Auto-publish on changes
  auto: false
  
  # Publish to Stoplight hosted docs
  hosted: true
