<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NotificationService API</title>
    <link rel="icon" type="image/svg+xml" href="/logo.svg">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
        }
        
        h1 {
            font-size: 3rem;
            margin: 0 0 1rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        p {
            font-size: 1.2rem;
            margin: 0 0 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .link {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem 2rem;
            text-decoration: none;
            color: white;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="/logo.svg" alt="NotificationService Logo" class="logo">
        <h1>NotificationService API</h1>
        <p>A comprehensive notification service supporting Email, SMS, and Push notifications with a powerful plugin architecture.</p>
        
        <div class="links">
            <a href="/scalar/v1" class="link">
                <span class="emoji">📚</span>
                API Documentation
            </a>
            <a href="/hangfire" class="link">
                <span class="emoji">⚙️</span>
                Hangfire Dashboard
            </a>
            <a href="/health/ready" class="link">
                <span class="emoji">💚</span>
                Health Check
            </a>
            <a href="/api/plugins" class="link">
                <span class="emoji">🔌</span>
                Plugins
            </a>
        </div>
    </div>
</body>
</html>
