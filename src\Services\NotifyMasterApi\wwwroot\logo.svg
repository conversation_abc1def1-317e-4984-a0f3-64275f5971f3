<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" fill="none">
  <defs>
    <!-- Main gradient -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2196f3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565c0;stop-opacity:1" />
    </linearGradient>
    
    <!-- Accent gradient -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc004e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c51162;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow effect -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Drop shadow -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="4" flood-color="#000" flood-opacity="0.25"/>
    </filter>
  </defs>
  
  <!-- Background circle with gradient -->
  <circle cx="32" cy="32" r="28" fill="url(#mainGradient)" filter="url(#shadow)"/>
  
  <!-- Inner circle for depth -->
  <circle cx="32" cy="32" r="24" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  
  <!-- Main notification bell -->
  <g transform="translate(32, 32)">
    <!-- Bell body -->
    <path d="M-8 -4c0-4.4 3.6-8 8-8s8 3.6 8 8v6c0 2.2 0.9 4.2 2.5 5.7l1.5 1.5c0.6 0.6 0.6 1.5 0 2.1-0.3 0.3-0.7 0.4-1.1 0.4H-12.9c-0.4 0-0.8-0.1-1.1-0.4-0.6-0.6-0.6-1.5 0-2.1l1.5-1.5C-10.9 6.2-10 4.2-10 2v-6z" 
          fill="white" 
          filter="url(#glow)"/>
    
    <!-- Bell handle -->
    <rect x="-1" y="-14" width="2" height="4" rx="1" fill="white" opacity="0.9"/>
    
    <!-- Bell clapper -->
    <circle cx="0" cy="-2" r="2" fill="white" opacity="0.8"/>
    
    <!-- Bell bottom -->
    <ellipse cx="0" cy="12" rx="10" ry="2" fill="rgba(255,255,255,0.3)"/>
  </g>
  
  <!-- Notification indicators -->
  <g>
    <!-- Primary notification dot -->
    <circle cx="48" cy="16" r="6" fill="url(#accentGradient)" filter="url(#shadow)"/>
    <circle cx="48" cy="16" r="4" fill="#ff4081"/>
    <circle cx="46" cy="14" r="1.5" fill="rgba(255,255,255,0.6)"/>
    
    <!-- Secondary notification dots -->
    <circle cx="16" cy="20" r="3" fill="#4caf50" opacity="0.8"/>
    <circle cx="16" cy="20" r="2" fill="#81c784"/>
    
    <circle cx="48" cy="48" r="3" fill="#ff9800" opacity="0.8"/>
    <circle cx="48" cy="48" r="2" fill="#ffb74d"/>
  </g>
  
  <!-- Signal waves -->
  <g opacity="0.6">
    <path d="M20 32 Q24 28 28 32 Q24 36 20 32" 
          fill="none" 
          stroke="white" 
          stroke-width="2" 
          stroke-linecap="round"/>
    <path d="M36 32 Q40 28 44 32 Q40 36 36 32" 
          fill="none" 
          stroke="white" 
          stroke-width="2" 
          stroke-linecap="round"/>
  </g>
  
  <!-- Shine effect -->
  <ellipse cx="24" cy="20" rx="4" ry="8" fill="white" opacity="0.3" transform="rotate(-30 24 20)"/>
  
  <!-- Brand text (optional, can be removed for icon-only version) -->
  <text x="32" y="58" 
        font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" 
        font-size="6" 
        font-weight="600" 
        text-anchor="middle" 
        fill="#1976d2">
    NotifyService
  </text>
</svg>
