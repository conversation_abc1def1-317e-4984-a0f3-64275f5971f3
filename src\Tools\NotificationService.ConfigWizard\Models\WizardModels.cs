using System.ComponentModel.DataAnnotations;

namespace NotificationService.ConfigWizard.Models;

public class DatabaseConfiguration
{
    public string Provider { get; set; } = "SqlServer";
    public string Host { get; set; } = "localhost";
    public int Port { get; set; } = 1433;
    public string DatabaseName { get; set; } = "NotificationService";
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool IntegratedSecurity { get; set; } = false;
    public bool TrustServerCertificate { get; set; } = true;
    public string ConnectionString => BuildConnectionString();

    private string BuildConnectionString()
    {
        return Provider.ToLower() switch
        {
            "sqlserver" => IntegratedSecurity
                ? $"Server={Host},{Port};Database={DatabaseName};Integrated Security=true;TrustServerCertificate={TrustServerCertificate};"
                : $"Server={Host},{Port};Database={DatabaseName};User Id={Username};Password={Password};TrustServerCertificate={TrustServerCertificate};",
            
            "postgresql" => $"Host={Host};Port={Port};Database={DatabaseName};Username={Username};Password={Password};",
            
            "sqlite" => $"Data Source={DatabaseName}.db;",
            
            "inmemory" => "InMemory",
            
            _ => throw new NotSupportedException($"Database provider '{Provider}' is not supported")
        };
    }
}

public class TenantConfiguration
{
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public string Domain { get; set; } = string.Empty;
    
    public string Key { get; set; } = string.Empty;
    
    public string Description { get; set; } = string.Empty;
    
    // Branding
    public string? LogoUrl { get; set; }
    public string PrimaryColor { get; set; } = "#1976d2";
    public string SecondaryColor { get; set; } = "#dc004e";
    public string AppTitle { get; set; } = "NotificationService";
    public string? FaviconUrl { get; set; }
}

public class AdminConfiguration
{
    [Required]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    public string LastName { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string Password { get; set; } = string.Empty;
    
    public string FullName => $"{FirstName} {LastName}";
}

public class SystemPreferences
{
    public string TimeZone { get; set; } = "UTC";
    public string LoggingLevel { get; set; } = "Information";
    public int DefaultRetentionDays { get; set; } = 90;
    public bool EnableTelemetry { get; set; } = true;
    public bool EnableMetrics { get; set; } = true;
    public bool EnableAuditLogging { get; set; } = true;
    public string DefaultLanguage { get; set; } = "en-US";
    public string DateFormat { get; set; } = "yyyy-MM-dd";
    public string TimeFormat { get; set; } = "HH:mm:ss";
}

public class WizardConfiguration
{
    public DatabaseConfiguration Database { get; set; } = new();
    public TenantConfiguration RootTenant { get; set; } = new();
    public AdminConfiguration SuperAdmin { get; set; } = new();
    public SystemPreferences Preferences { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string Version { get; set; } = "2.0.0";
}

public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    
    public void AddError(string error) => Errors.Add(error);
    public void AddWarning(string warning) => Warnings.Add(warning);
}

public class SetupProgress
{
    public string CurrentStep { get; set; } = string.Empty;
    public int StepNumber { get; set; }
    public int TotalSteps { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    public DateTime? CompletedAt { get; set; }
    public List<string> CompletedSteps { get; set; } = new();
    public Dictionary<string, object> StepData { get; set; } = new();
}

public enum DatabaseProvider
{
    SqlServer,
    PostgreSQL,
    SQLite,
    InMemory
}

public enum LoggingLevel
{
    Trace,
    Debug,
    Information,
    Warning,
    Error,
    Critical
}

public class DatabaseTestResult
{
    public bool CanConnect { get; set; }
    public bool DatabaseExists { get; set; }
    public bool HasTables { get; set; }
    public string? Error { get; set; }
    public string? Version { get; set; }
    public TimeSpan ConnectionTime { get; set; }
}

public class MigrationResult
{
    public bool Success { get; set; }
    public List<string> AppliedMigrations { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public TimeSpan Duration { get; set; }
}

public class ConfigurationSummary
{
    public string DatabaseProvider { get; set; } = string.Empty;
    public string DatabaseHost { get; set; } = string.Empty;
    public string DatabaseName { get; set; } = string.Empty;
    public string RootTenantName { get; set; } = string.Empty;
    public string RootTenantDomain { get; set; } = string.Empty;
    public string AdminEmail { get; set; } = string.Empty;
    public string AdminName { get; set; } = string.Empty;
    public string TimeZone { get; set; } = string.Empty;
    public string LoggingLevel { get; set; } = string.Empty;
    public bool TelemetryEnabled { get; set; }
    public int RetentionDays { get; set; }
}

public static class WizardConstants
{
    public const string ConfigFileName = "appsettings.Production.json";
    public const string InitializedMarkerFile = ".initialized";
    public const string BackupConfigSuffix = ".backup";
    
    public static readonly string[] SupportedDatabaseProviders = 
    {
        "SqlServer",
        "PostgreSQL", 
        "SQLite",
        "InMemory"
    };
    
    public static readonly string[] SupportedTimeZones = 
    {
        "UTC",
        "America/New_York",
        "America/Chicago", 
        "America/Denver",
        "America/Los_Angeles",
        "Europe/London",
        "Europe/Paris",
        "Europe/Berlin",
        "Asia/Tokyo",
        "Asia/Shanghai",
        "Australia/Sydney"
    };
    
    public static readonly string[] SupportedLanguages = 
    {
        "en-US",
        "en-GB", 
        "es-ES",
        "fr-FR",
        "de-DE",
        "it-IT",
        "pt-BR",
        "ja-JP",
        "zh-CN",
        "ko-KR"
    };
}
