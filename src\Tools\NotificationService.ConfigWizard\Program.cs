using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NotificationService.ConfigWizard.Models;
using NotificationService.ConfigWizard.Services;
using NotificationService.ConfigWizard.Wizards;
using Spectre.Console;
using System.CommandLine;

namespace NotificationService.ConfigWizard;

class Program
{
    static async Task<int> Main(string[] args)
    {
        // Setup dependency injection
        var services = new ServiceCollection();
        ConfigureServices(services);
        var serviceProvider = services.BuildServiceProvider();

        // Create root command
        var rootCommand = new RootCommand("NotificationService Configuration Wizard")
        {
            CreateSetupCommand(serviceProvider),
            CreateStatusCommand(serviceProvider),
            CreateResetCommand(serviceProvider)
        };

        return await rootCommand.InvokeAsync(args);
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        services.AddScoped<IDatabaseService, DatabaseService>();
        services.AddScoped<IConfigurationService, ConfigurationService>();
        services.AddScoped<ISetupWizard, SetupWizard>();
    }

    private static Command CreateSetupCommand(ServiceProvider serviceProvider)
    {
        var setupCommand = new Command("setup", "Run the configuration wizard");
        
        var forceOption = new Option<bool>(
            "--force", 
            "Force setup even if system is already initialized");
        
        var configFileOption = new Option<string?>(
            "--config", 
            "Path to configuration file");

        setupCommand.AddOption(forceOption);
        setupCommand.AddOption(configFileOption);

        setupCommand.SetHandler(async (bool force, string? configFile) =>
        {
            var wizard = serviceProvider.GetRequiredService<ISetupWizard>();
            await wizard.RunAsync(force, configFile);
        }, forceOption, configFileOption);

        return setupCommand;
    }

    private static Command CreateStatusCommand(ServiceProvider serviceProvider)
    {
        var statusCommand = new Command("status", "Check system initialization status");

        statusCommand.SetHandler(async () =>
        {
            var configService = serviceProvider.GetRequiredService<IConfigurationService>();
            var isInitialized = await configService.IsSystemInitializedAsync();

            var panel = new Panel(
                isInitialized 
                    ? "[green]✓ System is initialized and ready[/]"
                    : "[yellow]⚠ System is not initialized[/]")
            {
                Header = new PanelHeader(" System Status "),
                Border = BoxBorder.Rounded
            };

            AnsiConsole.Write(panel);

            if (isInitialized)
            {
                var config = await configService.LoadConfigurationAsync();
                if (config != null)
                {
                    var table = new Table()
                        .AddColumn("Setting")
                        .AddColumn("Value");

                    table.AddRow("Database Provider", config.Database.Provider);
                    table.AddRow("Root Tenant", config.RootTenant.Name);
                    table.AddRow("Admin Email", config.SuperAdmin.Email);
                    table.AddRow("Timezone", config.Preferences.TimeZone);

                    AnsiConsole.Write(table);
                }
            }
            else
            {
                AnsiConsole.MarkupLine("\n[yellow]Run 'setup' command to initialize the system.[/]");
            }
        });

        return statusCommand;
    }

    private static Command CreateResetCommand(ServiceProvider serviceProvider)
    {
        var resetCommand = new Command("reset", "Reset system configuration");

        var confirmOption = new Option<bool>(
            "--confirm", 
            "Confirm reset without prompting");

        resetCommand.AddOption(confirmOption);

        resetCommand.SetHandler(async (bool confirm) =>
        {
            if (!confirm)
            {
                var shouldReset = AnsiConsole.Confirm(
                    "[red]This will delete all configuration and require re-setup. Continue?[/]");
                
                if (!shouldReset)
                {
                    AnsiConsole.MarkupLine("[yellow]Reset cancelled.[/]");
                    return;
                }
            }

            try
            {
                // Delete configuration files
                if (File.Exists(WizardConstants.ConfigFileName))
                {
                    File.Delete(WizardConstants.ConfigFileName);
                }

                if (File.Exists(WizardConstants.InitializedMarkerFile))
                {
                    File.Delete(WizardConstants.InitializedMarkerFile);
                }

                AnsiConsole.MarkupLine("[green]✓ System configuration reset successfully.[/]");
                AnsiConsole.MarkupLine("[yellow]Run 'setup' command to reconfigure the system.[/]");
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗ Failed to reset configuration: {ex.Message}[/]");
            }
        }, confirmOption);

        return resetCommand;
    }
}

public interface ISetupWizard
{
    Task RunAsync(bool force = false, string? configFile = null);
}

public class SetupWizard : ISetupWizard
{
    private readonly IDatabaseService _databaseService;
    private readonly IConfigurationService _configurationService;
    private readonly ILogger<SetupWizard> _logger;

    public SetupWizard(
        IDatabaseService databaseService,
        IConfigurationService configurationService,
        ILogger<SetupWizard> logger)
    {
        _databaseService = databaseService;
        _configurationService = configurationService;
        _logger = logger;
    }

    public async Task RunAsync(bool force = false, string? configFile = null)
    {
        try
        {
            // Show welcome banner
            ShowWelcomeBanner();

            // Check if already initialized
            if (!force && await _configurationService.IsSystemInitializedAsync())
            {
                AnsiConsole.MarkupLine("[yellow]⚠ System is already initialized.[/]");
                AnsiConsole.MarkupLine("Use --force to reinitialize or 'reset' to clear configuration.");
                return;
            }

            var config = new WizardConfiguration();

            // Run wizard steps
            await RunDatabaseConfigurationStep(config);
            await RunSchemaInitializationStep(config);
            await RunRootTenantSetupStep(config);
            await RunSuperAdminSetupStep(config);
            await RunPreferencesStep(config);
            await RunSummaryAndConfirmationStep(config);
            await RunSaveConfigurationStep(config, configFile);

            // Show completion message
            ShowCompletionMessage();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Setup wizard failed");
            AnsiConsole.MarkupLine($"[red]✗ Setup failed: {ex.Message}[/]");
        }
    }

    private void ShowWelcomeBanner()
    {
        var banner = new FigletText("NotificationService")
            .Centered()
            .Color(Color.Blue);

        AnsiConsole.Write(banner);

        var panel = new Panel(
            "Welcome to the NotificationService Configuration Wizard!\n\n" +
            "This wizard will guide you through setting up your notification service:\n" +
            "• Database configuration and schema setup\n" +
            "• Root tenant and super admin creation\n" +
            "• System preferences and defaults\n\n" +
            "[dim]Press any key to continue...[/]")
        {
            Header = new PanelHeader(" Setup Wizard "),
            Border = BoxBorder.Rounded,
            BorderStyle = Style.Parse("blue")
        };

        AnsiConsole.Write(panel);
        Console.ReadKey(true);
        AnsiConsole.Clear();
    }

    private async Task RunDatabaseConfigurationStep(WizardConfiguration config)
    {
        AnsiConsole.MarkupLine("[bold blue]Step 1: Database Configuration[/]\n");

        // Database provider selection
        var provider = AnsiConsole.Prompt(
            new SelectionPrompt<string>()
                .Title("Select database provider:")
                .AddChoices(WizardConstants.SupportedDatabaseProviders));

        config.Database.Provider = provider;

        if (provider != "InMemory" && provider != "SQLite")
        {
            config.Database.Host = AnsiConsole.Ask<string>("Database host:", "localhost");
            
            var defaultPort = provider == "PostgreSQL" ? 5432 : 1433;
            config.Database.Port = AnsiConsole.Ask<int>("Database port:", defaultPort);
            
            config.Database.DatabaseName = AnsiConsole.Ask<string>("Database name:", "NotificationService");

            if (provider == "SqlServer")
            {
                config.Database.IntegratedSecurity = AnsiConsole.Confirm("Use integrated security?", false);
            }

            if (!config.Database.IntegratedSecurity)
            {
                config.Database.Username = AnsiConsole.Ask<string>("Username:");
                config.Database.Password = AnsiConsole.Prompt(
                    new TextPrompt<string>("Password:")
                        .Secret());
            }
        }
        else if (provider == "SQLite")
        {
            config.Database.DatabaseName = AnsiConsole.Ask<string>("Database file name:", "NotificationService");
        }

        // Test connection
        await AnsiConsole.Status()
            .StartAsync("Testing database connection...", async ctx =>
            {
                var testResult = await _databaseService.TestConnectionAsync(config.Database);
                
                if (testResult.CanConnect)
                {
                    AnsiConsole.MarkupLine("[green]✓ Database connection successful[/]");
                    if (testResult.Version != null)
                    {
                        AnsiConsole.MarkupLine($"[dim]Database version: {testResult.Version}[/]");
                    }
                }
                else
                {
                    AnsiConsole.MarkupLine($"[red]✗ Connection failed: {testResult.Error}[/]");
                    throw new InvalidOperationException("Database connection failed");
                }
            });

        AnsiConsole.WriteLine();
    }

    private async Task RunSchemaInitializationStep(WizardConfiguration config)
    {
        AnsiConsole.MarkupLine("[bold blue]Step 2: Schema Initialization[/]\n");

        await AnsiConsole.Status()
            .StartAsync("Initializing database schema...", async ctx =>
            {
                var migrationResult = await _databaseService.InitializeSchemaAsync(config.Database);
                
                if (migrationResult.Success)
                {
                    AnsiConsole.MarkupLine("[green]✓ Schema initialized successfully[/]");
                    if (migrationResult.AppliedMigrations.Any())
                    {
                        AnsiConsole.MarkupLine($"[dim]Applied {migrationResult.AppliedMigrations.Count} migrations[/]");
                    }
                }
                else
                {
                    AnsiConsole.MarkupLine("[red]✗ Schema initialization failed[/]");
                    foreach (var error in migrationResult.Errors)
                    {
                        AnsiConsole.MarkupLine($"[red]  {error}[/]");
                    }
                    throw new InvalidOperationException("Schema initialization failed");
                }
            });

        AnsiConsole.WriteLine();
    }

    private async Task RunRootTenantSetupStep(WizardConfiguration config)
    {
        AnsiConsole.MarkupLine("[bold blue]Step 3: Root Tenant Setup[/]\n");

        config.RootTenant.Name = AnsiConsole.Ask<string>("Organization name:");
        config.RootTenant.Domain = AnsiConsole.Ask<string>("Domain:", config.RootTenant.Name.ToLower().Replace(" ", ""));
        config.RootTenant.Description = AnsiConsole.Ask<string>("Description (optional):", "");

        // Optional branding
        if (AnsiConsole.Confirm("Configure branding?", false))
        {
            config.RootTenant.AppTitle = AnsiConsole.Ask<string>("Application title:", "NotificationService");
            config.RootTenant.PrimaryColor = AnsiConsole.Ask<string>("Primary color:", "#1976d2");
            config.RootTenant.SecondaryColor = AnsiConsole.Ask<string>("Secondary color:", "#dc004e");
            config.RootTenant.LogoUrl = AnsiConsole.Ask<string>("Logo URL (optional):", "");
            config.RootTenant.FaviconUrl = AnsiConsole.Ask<string>("Favicon URL (optional):", "");
        }

        AnsiConsole.WriteLine();
    }

    private async Task RunSuperAdminSetupStep(WizardConfiguration config)
    {
        AnsiConsole.MarkupLine("[bold blue]Step 4: Super Admin Setup[/]\n");

        config.SuperAdmin.FirstName = AnsiConsole.Ask<string>("First name:");
        config.SuperAdmin.LastName = AnsiConsole.Ask<string>("Last name:");
        config.SuperAdmin.Email = AnsiConsole.Ask<string>("Email address:");
        
        string password;
        string confirmPassword;
        
        do
        {
            password = AnsiConsole.Prompt(
                new TextPrompt<string>("Password (min 8 characters):")
                    .Secret()
                    .Validate(p => p.Length >= 8 ? ValidationResult.Success() : ValidationResult.Error("Password must be at least 8 characters")));
            
            confirmPassword = AnsiConsole.Prompt(
                new TextPrompt<string>("Confirm password:")
                    .Secret());
            
            if (password != confirmPassword)
            {
                AnsiConsole.MarkupLine("[red]Passwords do not match. Please try again.[/]");
            }
        } while (password != confirmPassword);

        config.SuperAdmin.Password = password;

        AnsiConsole.WriteLine();
    }

    private async Task RunPreferencesStep(WizardConfiguration config)
    {
        AnsiConsole.MarkupLine("[bold blue]Step 5: System Preferences[/]\n");

        config.Preferences.TimeZone = AnsiConsole.Prompt(
            new SelectionPrompt<string>()
                .Title("Select timezone:")
                .AddChoices(WizardConstants.SupportedTimeZones));

        config.Preferences.LoggingLevel = AnsiConsole.Prompt(
            new SelectionPrompt<string>()
                .Title("Select logging level:")
                .AddChoices("Trace", "Debug", "Information", "Warning", "Error", "Critical"));

        config.Preferences.DefaultRetentionDays = AnsiConsole.Ask<int>("Default retention days:", 90);
        config.Preferences.EnableTelemetry = AnsiConsole.Confirm("Enable telemetry?", true);
        config.Preferences.EnableMetrics = AnsiConsole.Confirm("Enable metrics?", true);
        config.Preferences.EnableAuditLogging = AnsiConsole.Confirm("Enable audit logging?", true);

        AnsiConsole.WriteLine();
    }

    private async Task RunSummaryAndConfirmationStep(WizardConfiguration config)
    {
        AnsiConsole.MarkupLine("[bold blue]Step 6: Summary & Confirmation[/]\n");

        var table = new Table()
            .Border(TableBorder.Rounded)
            .AddColumn("[bold]Setting[/]")
            .AddColumn("[bold]Value[/]");

        table.AddRow("Database Provider", config.Database.Provider);
        table.AddRow("Database Host", config.Database.Host);
        table.AddRow("Database Name", config.Database.DatabaseName);
        table.AddRow("Organization", config.RootTenant.Name);
        table.AddRow("Domain", config.RootTenant.Domain);
        table.AddRow("Admin Name", config.SuperAdmin.FullName);
        table.AddRow("Admin Email", config.SuperAdmin.Email);
        table.AddRow("Timezone", config.Preferences.TimeZone);
        table.AddRow("Logging Level", config.Preferences.LoggingLevel);
        table.AddRow("Retention Days", config.Preferences.DefaultRetentionDays.ToString());

        AnsiConsole.Write(table);

        if (!AnsiConsole.Confirm("\n[yellow]Proceed with this configuration?[/]"))
        {
            throw new OperationCanceledException("Setup cancelled by user");
        }

        AnsiConsole.WriteLine();
    }

    private async Task RunSaveConfigurationStep(WizardConfiguration config, string? configFile)
    {
        AnsiConsole.MarkupLine("[bold blue]Step 7: Saving Configuration[/]\n");

        await AnsiConsole.Status()
            .StartAsync("Saving configuration...", async ctx =>
            {
                var success = await _configurationService.SaveConfigurationAsync(config, configFile);
                if (!success)
                {
                    throw new InvalidOperationException("Failed to save configuration");
                }

                await _configurationService.CreateInitializedMarkerAsync();
            });

        AnsiConsole.MarkupLine("[green]✓ Configuration saved successfully[/]");
        AnsiConsole.WriteLine();
    }

    private void ShowCompletionMessage()
    {
        var panel = new Panel(
            "[green]✓ Setup completed successfully![/]\n\n" +
            "Your NotificationService is now configured and ready to use.\n\n" +
            "Next steps:\n" +
            "• Start the NotificationService API\n" +
            "• Access the web portal\n" +
            "• Configure your first plugins\n\n" +
            "[dim]Thank you for using NotificationService![/]")
        {
            Header = new PanelHeader(" Setup Complete "),
            Border = BoxBorder.Rounded,
            BorderStyle = Style.Parse("green")
        };

        AnsiConsole.Write(panel);
    }
}
