using Microsoft.Extensions.Logging;
using NotificationService.ConfigWizard.Models;
using System.Text.Json;
using System.Text.Json.Serialization;
using BCrypt.Net;

namespace NotificationService.ConfigWizard.Services;

public interface IConfigurationService
{
    Task<bool> SaveConfigurationAsync(WizardConfiguration config, string? filePath = null);
    Task<WizardConfiguration?> LoadConfigurationAsync(string? filePath = null);
    Task<bool> CreateInitializedMarkerAsync();
    Task<bool> IsSystemInitializedAsync();
    Task<bool> BackupExistingConfigAsync(string? filePath = null);
    Task<ValidationResult> ValidateConfigurationAsync(WizardConfiguration config);
    string GenerateSecureKey(int length = 32);
    string HashPassword(string password);
}

public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
    }

    public async Task<bool> SaveConfigurationAsync(WizardConfiguration config, string? filePath = null)
    {
        try
        {
            filePath ??= WizardConstants.ConfigFileName;
            
            // Create backup if file exists
            if (File.Exists(filePath))
            {
                await BackupExistingConfigAsync(filePath);
            }

            // Generate secure keys if not provided
            if (string.IsNullOrEmpty(config.RootTenant.Key))
            {
                config.RootTenant.Key = GenerateSecureKey();
            }

            // Hash password if not already hashed
            if (!config.SuperAdmin.Password.StartsWith("$2"))
            {
                config.SuperAdmin.Password = HashPassword(config.SuperAdmin.Password);
            }

            // Build the complete configuration object
            var appSettings = BuildAppSettingsObject(config);

            // Write to file
            var json = JsonSerializer.Serialize(appSettings, _jsonOptions);
            await File.WriteAllTextAsync(filePath, json);

            _logger.LogInformation("Configuration saved to {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save configuration to {FilePath}", filePath);
            return false;
        }
    }

    public async Task<WizardConfiguration?> LoadConfigurationAsync(string? filePath = null)
    {
        try
        {
            filePath ??= WizardConstants.ConfigFileName;
            
            if (!File.Exists(filePath))
            {
                return null;
            }

            var json = await File.ReadAllTextAsync(filePath);
            var appSettings = JsonSerializer.Deserialize<AppSettingsRoot>(json, _jsonOptions);

            if (appSettings == null)
            {
                return null;
            }

            return ExtractWizardConfiguration(appSettings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load configuration from {FilePath}", filePath);
            return null;
        }
    }

    public async Task<bool> CreateInitializedMarkerAsync()
    {
        try
        {
            var markerData = new
            {
                InitializedAt = DateTime.UtcNow,
                Version = "2.0.0",
                InitializedBy = "ConfigWizard"
            };

            var json = JsonSerializer.Serialize(markerData, _jsonOptions);
            await File.WriteAllTextAsync(WizardConstants.InitializedMarkerFile, json);

            _logger.LogInformation("Initialized marker created");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create initialized marker");
            return false;
        }
    }

    public async Task<bool> IsSystemInitializedAsync()
    {
        return File.Exists(WizardConstants.InitializedMarkerFile) && 
               File.Exists(WizardConstants.ConfigFileName);
    }

    public async Task<bool> BackupExistingConfigAsync(string? filePath = null)
    {
        try
        {
            filePath ??= WizardConstants.ConfigFileName;
            
            if (!File.Exists(filePath))
            {
                return true; // Nothing to backup
            }

            var backupPath = $"{filePath}{WizardConstants.BackupConfigSuffix}.{DateTime.UtcNow:yyyyMMdd_HHmmss}";
            File.Copy(filePath, backupPath);

            _logger.LogInformation("Configuration backed up to {BackupPath}", backupPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to backup configuration");
            return false;
        }
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(WizardConfiguration config)
    {
        var result = new ValidationResult { IsValid = true };

        // Validate database configuration
        if (string.IsNullOrWhiteSpace(config.Database.DatabaseName))
        {
            result.AddError("Database name is required");
            result.IsValid = false;
        }

        // Validate tenant configuration
        if (string.IsNullOrWhiteSpace(config.RootTenant.Name))
        {
            result.AddError("Root tenant name is required");
            result.IsValid = false;
        }

        if (string.IsNullOrWhiteSpace(config.RootTenant.Domain))
        {
            result.AddError("Root tenant domain is required");
            result.IsValid = false;
        }

        // Validate admin configuration
        if (string.IsNullOrWhiteSpace(config.SuperAdmin.Email))
        {
            result.AddError("Super admin email is required");
            result.IsValid = false;
        }

        if (string.IsNullOrWhiteSpace(config.SuperAdmin.Password))
        {
            result.AddError("Super admin password is required");
            result.IsValid = false;
        }
        else if (config.SuperAdmin.Password.Length < 8)
        {
            result.AddError("Super admin password must be at least 8 characters");
            result.IsValid = false;
        }

        // Validate preferences
        if (!WizardConstants.SupportedTimeZones.Contains(config.Preferences.TimeZone))
        {
            result.AddWarning($"Unsupported timezone: {config.Preferences.TimeZone}");
        }

        return result;
    }

    public string GenerateSecureKey(int length = 32)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
    }

    private object BuildAppSettingsObject(WizardConfiguration config)
    {
        return new
        {
            Logging = new
            {
                LogLevel = new
                {
                    Default = config.Preferences.LoggingLevel,
                    Microsoft = "Warning",
                    System = "Warning"
                }
            },
            ConnectionStrings = new
            {
                DefaultConnection = config.Database.ConnectionString
            },
            Database = new
            {
                Provider = config.Database.Provider,
                MigrationsAssembly = "NotifyMasterApi"
            },
            RootTenant = new
            {
                Id = "root",
                Name = config.RootTenant.Name,
                Domain = config.RootTenant.Domain,
                Key = config.RootTenant.Key,
                Description = config.RootTenant.Description,
                Whitelabel = new
                {
                    AppTitle = config.RootTenant.AppTitle,
                    LogoUrl = config.RootTenant.LogoUrl,
                    FaviconUrl = config.RootTenant.FaviconUrl,
                    PrimaryColor = config.RootTenant.PrimaryColor,
                    SecondaryColor = config.RootTenant.SecondaryColor
                }
            },
            SuperAdmin = new
            {
                Email = config.SuperAdmin.Email,
                FirstName = config.SuperAdmin.FirstName,
                LastName = config.SuperAdmin.LastName,
                PasswordHash = config.SuperAdmin.Password
            },
            SystemPreferences = new
            {
                TimeZone = config.Preferences.TimeZone,
                DefaultLanguage = config.Preferences.DefaultLanguage,
                DateFormat = config.Preferences.DateFormat,
                TimeFormat = config.Preferences.TimeFormat,
                DefaultRetentionDays = config.Preferences.DefaultRetentionDays,
                EnableTelemetry = config.Preferences.EnableTelemetry,
                EnableMetrics = config.Preferences.EnableMetrics,
                EnableAuditLogging = config.Preferences.EnableAuditLogging
            },
            Features = new
            {
                EnableSignalR = true,
                EnableNotifications = true,
                EnableAuditLogging = config.Preferences.EnableAuditLogging,
                EnableMultiTenancy = true,
                EnableMetrics = config.Preferences.EnableMetrics
            },
            Setup = new
            {
                IsInitialized = true,
                InitializedAt = config.CreatedAt,
                Version = config.Version,
                InitializedBy = "ConfigWizard"
            }
        };
    }

    private WizardConfiguration ExtractWizardConfiguration(AppSettingsRoot appSettings)
    {
        var config = new WizardConfiguration();

        // Extract database configuration
        if (appSettings.ConnectionStrings?.DefaultConnection != null)
        {
            // Parse connection string to extract components
            // This is a simplified extraction - in reality you'd need more robust parsing
            config.Database.Provider = appSettings.Database?.Provider ?? "SqlServer";
        }

        // Extract tenant configuration
        if (appSettings.RootTenant != null)
        {
            config.RootTenant.Name = appSettings.RootTenant.Name ?? "";
            config.RootTenant.Domain = appSettings.RootTenant.Domain ?? "";
            config.RootTenant.Key = appSettings.RootTenant.Key ?? "";
            config.RootTenant.Description = appSettings.RootTenant.Description ?? "";
            
            if (appSettings.RootTenant.Whitelabel != null)
            {
                config.RootTenant.AppTitle = appSettings.RootTenant.Whitelabel.AppTitle ?? "NotificationService";
                config.RootTenant.LogoUrl = appSettings.RootTenant.Whitelabel.LogoUrl;
                config.RootTenant.FaviconUrl = appSettings.RootTenant.Whitelabel.FaviconUrl;
                config.RootTenant.PrimaryColor = appSettings.RootTenant.Whitelabel.PrimaryColor ?? "#1976d2";
                config.RootTenant.SecondaryColor = appSettings.RootTenant.Whitelabel.SecondaryColor ?? "#dc004e";
            }
        }

        // Extract admin configuration
        if (appSettings.SuperAdmin != null)
        {
            config.SuperAdmin.Email = appSettings.SuperAdmin.Email ?? "";
            config.SuperAdmin.FirstName = appSettings.SuperAdmin.FirstName ?? "";
            config.SuperAdmin.LastName = appSettings.SuperAdmin.LastName ?? "";
            config.SuperAdmin.Password = appSettings.SuperAdmin.PasswordHash ?? "";
        }

        // Extract preferences
        if (appSettings.SystemPreferences != null)
        {
            config.Preferences.TimeZone = appSettings.SystemPreferences.TimeZone ?? "UTC";
            config.Preferences.DefaultLanguage = appSettings.SystemPreferences.DefaultLanguage ?? "en-US";
            config.Preferences.DateFormat = appSettings.SystemPreferences.DateFormat ?? "yyyy-MM-dd";
            config.Preferences.TimeFormat = appSettings.SystemPreferences.TimeFormat ?? "HH:mm:ss";
            config.Preferences.DefaultRetentionDays = appSettings.SystemPreferences.DefaultRetentionDays ?? 90;
            config.Preferences.EnableTelemetry = appSettings.SystemPreferences.EnableTelemetry ?? true;
            config.Preferences.EnableMetrics = appSettings.SystemPreferences.EnableMetrics ?? true;
            config.Preferences.EnableAuditLogging = appSettings.SystemPreferences.EnableAuditLogging ?? true;
        }

        return config;
    }
}

// Helper classes for JSON deserialization
public class AppSettingsRoot
{
    public ConnectionStrings? ConnectionStrings { get; set; }
    public DatabaseConfig? Database { get; set; }
    public RootTenantConfig? RootTenant { get; set; }
    public SuperAdminConfig? SuperAdmin { get; set; }
    public SystemPreferencesConfig? SystemPreferences { get; set; }
}

public class ConnectionStrings
{
    public string? DefaultConnection { get; set; }
}

public class DatabaseConfig
{
    public string? Provider { get; set; }
    public string? MigrationsAssembly { get; set; }
}

public class RootTenantConfig
{
    public string? Id { get; set; }
    public string? Name { get; set; }
    public string? Domain { get; set; }
    public string? Key { get; set; }
    public string? Description { get; set; }
    public WhitelabelConfig? Whitelabel { get; set; }
}

public class WhitelabelConfig
{
    public string? AppTitle { get; set; }
    public string? LogoUrl { get; set; }
    public string? FaviconUrl { get; set; }
    public string? PrimaryColor { get; set; }
    public string? SecondaryColor { get; set; }
}

public class SuperAdminConfig
{
    public string? Email { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? PasswordHash { get; set; }
}

public class SystemPreferencesConfig
{
    public string? TimeZone { get; set; }
    public string? DefaultLanguage { get; set; }
    public string? DateFormat { get; set; }
    public string? TimeFormat { get; set; }
    public int? DefaultRetentionDays { get; set; }
    public bool? EnableTelemetry { get; set; }
    public bool? EnableMetrics { get; set; }
    public bool? EnableAuditLogging { get; set; }
}
