<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
            <NotAuthorized>
                @if (context.User.Identity?.IsAuthenticated != true)
                {
                    <RedirectToLogin />
                }
                else
                {
                    <div class="unauthorized-container">
                        <MudContainer MaxWidth="MaxWidth.Medium" Class="text-center pa-8">
                            <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Error" Class="mb-4" />
                            <MudText Typo="Typo.h4" Class="mb-2">Access Denied</MudText>
                            <MudText Typo="Typo.body1" Class="mb-4">
                                You don't have permission to access this page.
                            </MudText>
                            <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/dashboard">
                                Go to Dashboard
                            </MudButton>
                        </MudContainer>
                    </div>
                }
            </NotAuthorized>
            <Authorizing>
                <div class="loading-container">
                    <MudContainer MaxWidth="MaxWidth.Medium" Class="text-center pa-8">
                        <MudProgressCircular Size="Size.Large" Indeterminate="true" Class="mb-4" />
                        <MudText Typo="Typo.h6">Loading...</MudText>
                    </MudContainer>
                </div>
            </Authorizing>
        </AuthorizeRouteView>
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <div class="not-found-container">
                <MudContainer MaxWidth="MaxWidth.Medium" Class="text-center pa-8">
                    <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Warning" Class="mb-4" />
                    <MudText Typo="Typo.h4" Class="mb-2">Page Not Found</MudText>
                    <MudText Typo="Typo.body1" Class="mb-4">
                        The page you're looking for doesn't exist.
                    </MudText>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/dashboard">
                        Go to Dashboard
                    </MudButton>
                </MudContainer>
            </div>
        </LayoutView>
    </NotFound>
</Router>

<style>
    .unauthorized-container,
    .loading-container,
    .not-found-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--mud-palette-background-grey);
    }
</style>
