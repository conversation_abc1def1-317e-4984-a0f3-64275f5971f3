namespace NotificationPortal.Models;

public class User
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName => $"{FirstName} {LastName}".Trim();
    public string TenantId { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public bool IsSubUser { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? LastLoginAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class Tenant
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public TenantSettings Settings { get; set; } = new();
    public WhitelabelSettings Whitelabel { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TenantSettings
{
    public int MaxUsers { get; set; } = 100;
    public int MaxApiCallsPerHour { get; set; } = 10000;
    public List<string> AllowedFeatures { get; set; } = new();
    public string DefaultLanguage { get; set; } = "en";
    public string TimeZone { get; set; } = "UTC";
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

public class WhitelabelSettings
{
    public string? LogoUrl { get; set; }
    public string? FaviconUrl { get; set; }
    public string AppTitle { get; set; } = "NotificationService";
    public string PrimaryColor { get; set; } = "#1976d2";
    public string SecondaryColor { get; set; } = "#dc004e";
    public string Theme { get; set; } = "light";
    public Dictionary<string, string> CustomCss { get; set; } = new();
}

public class Role
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string TenantId { get; set; } = string.Empty;
    public List<string> Permissions { get; set; } = new();
    public bool IsSystemRole { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

public class Plugin
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsEnabled { get; set; }
    public bool IsLoaded { get; set; }
    public DateTime? LoadedAt { get; set; }
    public PluginHealth Health { get; set; } = new();
    public PluginMetrics Metrics { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class PluginHealth
{
    public bool IsHealthy { get; set; }
    public string Status { get; set; } = "Unknown";
    public DateTime LastCheck { get; set; } = DateTime.UtcNow;
    public string? ErrorMessage { get; set; }
}

public class PluginMetrics
{
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public double SuccessRate => TotalRequests == 0 ? 0 : (double)SuccessfulRequests / TotalRequests * 100;
    public double AverageResponseTime { get; set; }
    public DateTime LastReset { get; set; } = DateTime.UtcNow;
}

public class ApiKey
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? ExpiresAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public long UsageCount { get; set; }
    public List<string> Scopes { get; set; } = new();
}

public class Template
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string TenantId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty; // Email, SMS, Push, etc.
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? HtmlBody { get; set; }
    public List<string> Variables { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}

public class AuditLog
{
    public string Id { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string Resource { get; set; } = string.Empty;
    public string? ResourceId { get; set; }
    public string? Details { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class SystemEvent
{
    public string Id { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = "Info"; // Info, Warning, Error, Critical
    public string? TenantId { get; set; }
    public string? UserId { get; set; }
    public bool IsRead { get; set; } = false;
    public bool IsAcknowledged { get; set; } = false;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Data { get; set; } = new();
}

public class DashboardStats
{
    public SystemOverview System { get; set; } = new();
    public QueueStats Queue { get; set; } = new();
    public List<Plugin> Plugins { get; set; } = new();
    public UsageStats Usage { get; set; } = new();
}

public class SystemOverview
{
    public string Status { get; set; } = "Running";
    public string Version { get; set; } = "2.0.0";
    public TimeSpan Uptime { get; set; }
    public int LoadedPlugins { get; set; }
    public int ActiveTenants { get; set; }
    public int TotalUsers { get; set; }
}

public class QueueStats
{
    public int PendingMessages { get; set; }
    public int ProcessingMessages { get; set; }
    public int CompletedToday { get; set; }
    public int FailedToday { get; set; }
    public double AverageProcessingTime { get; set; }
}

public class UsageStats
{
    public int EmailsSentToday { get; set; }
    public int SmsSentToday { get; set; }
    public int PushSentToday { get; set; }
    public int ApiCallsToday { get; set; }
    public List<HourlyUsage> HourlyData { get; set; } = new();
}

public class HourlyUsage
{
    public DateTime Hour { get; set; }
    public int Count { get; set; }
    public string Type { get; set; } = string.Empty;
}

public class SystemHealth
{
    public string Status { get; set; } = "Healthy";
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public int ActiveConnections { get; set; }
    public TimeSpan Uptime { get; set; }
    public DateTime LastCheck { get; set; } = DateTime.UtcNow;
}

public class MessageVolumeChart
{
    public List<ChartDataPoint> EmailData { get; set; } = new();
    public List<ChartDataPoint> SmsData { get; set; } = new();
    public List<ChartDataPoint> PushData { get; set; } = new();
    public List<ChartDataPoint> MessagingData { get; set; } = new();
}

public class ChartDataPoint
{
    public DateTime Timestamp { get; set; }
    public double Value { get; set; }
    public string Label { get; set; } = string.Empty;
}

public class ActivityFeedItem
{
    public string Id { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string Severity { get; set; } = "Info";
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class LogEntry
{
    public string Id { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string Level { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? Exception { get; set; }
    public string? TenantId { get; set; }
    public string? UserId { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

public class NotificationSettings
{
    public bool EmailNotifications { get; set; } = true;
    public bool SmsNotifications { get; set; } = false;
    public bool PushNotifications { get; set; } = true;
    public bool InAppNotifications { get; set; } = true;
    public List<string> SubscribedEvents { get; set; } = new();
    public string NotificationFrequency { get; set; } = "Immediate"; // Immediate, Hourly, Daily
}

public class UserDevice
{
    public string Id { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public string DeviceType { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public DateTime FirstSeen { get; set; } = DateTime.UtcNow;
    public DateTime LastSeen { get; set; } = DateTime.UtcNow;
    public bool IsActive { get; set; } = true;
    public string? Location { get; set; }
}

public class BreadcrumbItem
{
    public string Text { get; set; } = string.Empty;
    public string? Href { get; set; }
    public string? Icon { get; set; }
    public bool IsActive { get; set; }
}

public class PermissionGroup
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Permission> Permissions { get; set; } = new();
}

public class Permission
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsSystemPermission { get; set; } = false;
}
