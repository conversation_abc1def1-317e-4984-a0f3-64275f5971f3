using MudBlazor;
using Microsoft.Extensions.Logging;
using Blazored.LocalStorage;

namespace NotificationPortal.Services;

public interface IThemeService
{
    MudTheme GetCurrentTheme();
    Task<bool> IsDarkModeAsync();
    Task SetDarkModeAsync(bool isDark);
    Task<string> GetPrimaryColorAsync();
    Task SetPrimaryColorAsync(string color);
    Task<string> GetSecondaryColorAsync();
    Task SetSecondaryColorAsync(string color);
    Task ApplyTenantBrandingAsync(string? primaryColor, string? secondaryColor);
    event Action<MudTheme> OnThemeChanged;
}

public class ThemeService : IThemeService
{
    private readonly ILocalStorageService _localStorage;
    private MudTheme _currentTheme;
    private bool _isDarkMode = false;

    public event Action<MudTheme>? OnThemeChanged;

    public ThemeService(ILocalStorageService localStorage)
    {
        _localStorage = localStorage;
        _currentTheme = CreateDefaultTheme();
    }

    public MudTheme GetCurrentTheme()
    {
        return _currentTheme;
    }

    public async Task<bool> IsDarkModeAsync()
    {
        try
        {
            _isDarkMode = await _localStorage.GetItemAsync<bool>("theme_dark_mode");
        }
        catch
        {
            _isDarkMode = false;
        }
        return _isDarkMode;
    }

    public async Task SetDarkModeAsync(bool isDark)
    {
        _isDarkMode = isDark;
        await _localStorage.SetItemAsync("theme_dark_mode", isDark);
        
        _currentTheme = CreateTheme(_isDarkMode);
        OnThemeChanged?.Invoke(_currentTheme);
    }

    public async Task<string> GetPrimaryColorAsync()
    {
        try
        {
            return await _localStorage.GetItemAsync<string>("theme_primary_color") ?? "#2563eb";
        }
        catch
        {
            return "#2563eb";
        }
    }

    public async Task SetPrimaryColorAsync(string color)
    {
        await _localStorage.SetItemAsync("theme_primary_color", color);
        _currentTheme = CreateTheme(_isDarkMode, color);
        OnThemeChanged?.Invoke(_currentTheme);
    }

    public async Task<string> GetSecondaryColorAsync()
    {
        try
        {
            return await _localStorage.GetItemAsync<string>("theme_secondary_color") ?? "#7c3aed";
        }
        catch
        {
            return "#7c3aed";
        }
    }

    public async Task SetSecondaryColorAsync(string color)
    {
        await _localStorage.SetItemAsync("theme_secondary_color", color);
        _currentTheme = CreateTheme(_isDarkMode, null, color);
        OnThemeChanged?.Invoke(_currentTheme);
    }

    public async Task ApplyTenantBrandingAsync(string? primaryColor, string? secondaryColor)
    {
        var primary = primaryColor ?? await GetPrimaryColorAsync();
        var secondary = secondaryColor ?? await GetSecondaryColorAsync();
        
        _currentTheme = CreateTheme(_isDarkMode, primary, secondary);
        OnThemeChanged?.Invoke(_currentTheme);
    }

    private MudTheme CreateDefaultTheme()
    {
        return CreateTheme(false);
    }

    private MudTheme CreateTheme(bool isDark, string? primaryColor = null, string? secondaryColor = null)
    {
        var primary = primaryColor ?? "#2563eb";
        var secondary = secondaryColor ?? "#7c3aed";

        return new MudTheme
        {
            PaletteLight = new PaletteLight
            {
                Primary = primary,
                Secondary = secondary,
                Success = "#059669",
                Warning = "#d97706",
                Error = "#dc2626",
                Info = "#0891b2",
                Background = "#ffffff",
                BackgroundGrey = "#f8fafc",
                Surface = "#ffffff",
                DrawerBackground = "#ffffff",
                AppbarBackground = "#ffffff",
                TextPrimary = "#0f172a",
                TextSecondary = "#64748b",
                TextDisabled = "#cbd5e1",
                ActionDefault = "#64748b",
                ActionDisabled = "#cbd5e1",
                ActionDisabledBackground = "#f1f5f9",
                Divider = "#e2e8f0",
                DividerLight = "#f1f5f9",
                TableLines = "#e2e8f0",
                LinesDefault = "#e2e8f0",
                LinesInputs = "#d1d5db",
                GrayDefault = "#64748b",
                GrayLight = "#f1f5f9",
                GrayLighter = "#f8fafc",
                GrayDark = "#374151",
                GrayDarker = "#1f2937",
                OverlayDark = "rgba(33,33,33,0.4980392156862745)",
                OverlayLight = "rgba(255,255,255,0.4980392156862745)"
            },
            PaletteDark = new PaletteDark
            {
                Primary = "#3b82f6",
                Secondary = "#8b5cf6",
                Success = "#10b981",
                Warning = "#f59e0b",
                Error = "#ef4444",
                Info = "#06b6d4",
                Background = "#0f172a",
                BackgroundGrey = "#1e293b",
                Surface = "#1e293b",
                DrawerBackground = "#1e293b",
                AppbarBackground = "#1e293b",
                TextPrimary = "#f8fafc",
                TextSecondary = "#cbd5e1",
                TextDisabled = "#64748b",
                ActionDefault = "#cbd5e1",
                ActionDisabled = "#64748b",
                ActionDisabledBackground = "#334155",
                Divider = "#334155",
                DividerLight = "#475569",
                TableLines = "#334155",
                LinesDefault = "#334155",
                LinesInputs = "#475569",
                GrayDefault = "#cbd5e1",
                GrayLight = "#475569",
                GrayLighter = "#334155",
                GrayDark = "#94a3b8",
                GrayDarker = "#e2e8f0",
                OverlayDark = "rgba(33,33,33,0.4980392156862745)",
                OverlayLight = "rgba(255,255,255,0.4980392156862745)"
            },
            Typography = new Typography
            {
                Default = new Default
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "0.875rem",
                    FontWeight = 400,
                    LineHeight = 1.43,
                    LetterSpacing = "0.01071em"
                },
                H1 = new H1
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "6rem",
                    FontWeight = 700,
                    LineHeight = 1.167,
                    LetterSpacing = "-0.01562em"
                },
                H2 = new H2
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "3.75rem",
                    FontWeight = 700,
                    LineHeight = 1.2,
                    LetterSpacing = "-0.00833em"
                },
                H3 = new H3
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "3rem",
                    FontWeight = 700,
                    LineHeight = 1.167,
                    LetterSpacing = "0em"
                },
                H4 = new H4
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "2.125rem",
                    FontWeight = 700,
                    LineHeight = 1.235,
                    LetterSpacing = "0.00735em"
                },
                H5 = new H5
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "1.5rem",
                    FontWeight = 600,
                    LineHeight = 1.334,
                    LetterSpacing = "0em"
                },
                H6 = new H6
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "1.25rem",
                    FontWeight = 600,
                    LineHeight = 1.6,
                    LetterSpacing = "0.0075em"
                },
                Subtitle1 = new Subtitle1
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "1rem",
                    FontWeight = 500,
                    LineHeight = 1.75,
                    LetterSpacing = "0.00938em"
                },
                Subtitle2 = new Subtitle2
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "0.875rem",
                    FontWeight = 500,
                    LineHeight = 1.57,
                    LetterSpacing = "0.00714em"
                },
                Body1 = new Body1
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "1rem",
                    FontWeight = 400,
                    LineHeight = 1.5,
                    LetterSpacing = "0.00938em"
                },
                Body2 = new Body2
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "0.875rem",
                    FontWeight = 400,
                    LineHeight = 1.43,
                    LetterSpacing = "0.01071em"
                },
                Button = new Button
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "0.875rem",
                    FontWeight = 500,
                    LineHeight = 1.75,
                    LetterSpacing = "0.02857em",
                    TextTransform = "none"
                },
                Caption = new Caption
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "0.75rem",
                    FontWeight = 500,
                    LineHeight = 1.66,
                    LetterSpacing = "0.03333em"
                },
                Overline = new Overline
                {
                    FontFamily = new[] { "Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif" },
                    FontSize = "0.75rem",
                    FontWeight = 600,
                    LineHeight = 2.66,
                    LetterSpacing = "0.08333em"
                }
            },
            Shadows = new Shadow(),
            LayoutProperties = new LayoutProperties
            {
                DefaultBorderRadius = "8px",
                AppbarHeight = "64px",
                DrawerWidthLeft = "280px",
                DrawerWidthRight = "280px"
            },
            ZIndex = new ZIndex()
        };
    }
}

public interface ISetupStatusService
{
    Task<bool> IsSystemInitializedAsync();
    Task<SetupStatusResponse?> GetSetupStatusAsync();
    Task<bool> CheckServerConnectionAsync(string serverUrl);
}

public class SetupStatusService : ISetupStatusService
{
    private readonly IApiService _apiService;
    private readonly ILogger<SetupStatusService> _logger;

    public SetupStatusService(IApiService apiService, ILogger<SetupStatusService> logger)
    {
        _apiService = apiService;
        _logger = logger;
    }

    public async Task<bool> IsSystemInitializedAsync()
    {
        try
        {
            var status = await GetSetupStatusAsync();
            return status?.IsInitialized ?? false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check system initialization status");
            return false;
        }
    }

    public async Task<SetupStatusResponse?> GetSetupStatusAsync()
    {
        try
        {
            return await _apiService.GetAsync<SetupStatusResponse>("/health/setup-status");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get setup status");
            return null;
        }
    }

    public async Task<bool> CheckServerConnectionAsync(string serverUrl)
    {
        try
        {
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10);
            
            var response = await httpClient.GetAsync($"{serverUrl.TrimEnd('/')}/health/live");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to server at {ServerUrl}", serverUrl);
            return false;
        }
    }
}

public class SetupStatusResponse
{
    public bool IsInitialized { get; set; }
    public string Version { get; set; } = string.Empty;
    public string? RootTenantName { get; set; }
    public DateTime? InitializedAt { get; set; }
    public string? InitializedBy { get; set; }
    public Dictionary<string, bool> ComponentStatus { get; set; } = new();
}
