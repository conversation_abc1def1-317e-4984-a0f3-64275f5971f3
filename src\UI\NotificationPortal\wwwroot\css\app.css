/* Global Styles */
html, body {
    font-family: '<PERSON>o', sans-serif;
    margin: 0;
    padding: 0;
    height: 100%;
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

/* Custom MudBlazor overrides */
.mud-theme-primary {
    --mud-palette-primary: #1976d2;
    --mud-palette-primary-text: #ffffff;
    --mud-palette-primary-hover: #1565c0;
}

.mud-theme-secondary {
    --mud-palette-secondary: #dc004e;
    --mud-palette-secondary-text: #ffffff;
    --mud-palette-secondary-hover: #c51162;
}

/* Layout Styles */
.main-content {
    padding-top: 64px; /* Account for app bar height */
}

.app-bar {
    z-index: 1300;
}

/* Card Styles */
.stat-card {
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Navigation Styles */
.mud-nav-link {
    border-radius: 0 25px 25px 0;
    margin: 2px 8px 2px 0;
    transition: all 0.2s ease;
}

.mud-nav-link:hover {
    background-color: rgba(25, 118, 210, 0.08);
}

.mud-nav-link.active {
    background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-secondary) 100%);
    color: white;
    font-weight: 500;
}

/* Form Styles */
.login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-paper {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* Table Styles */
.mud-table-container {
    border-radius: 8px;
    overflow: hidden;
}

.mud-table-head {
    background-color: var(--mud-palette-grey-lighten-4);
}

/* Button Styles */
.mud-button-filled-primary {
    background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-secondary) 100%);
    border: none;
    transition: all 0.3s ease;
}

.mud-button-filled-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

/* Chart Styles */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    border: 2px dashed var(--mud-palette-divider);
    border-radius: 8px;
    background-color: var(--mud-palette-background-grey);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.success {
    background-color: var(--mud-palette-success);
}

.status-dot.error {
    background-color: var(--mud-palette-error);
}

.status-dot.warning {
    background-color: var(--mud-palette-warning);
}

.status-dot.info {
    background-color: var(--mud-palette-info);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 16px 8px;
    }
    
    .stat-card {
        margin-bottom: 16px;
    }
    
    .mud-drawer {
        width: 240px;
    }
}

/* Dark Theme Overrides */
.mud-theme-dark {
    --mud-palette-background: #121212;
    --mud-palette-background-grey: #1e1e1e;
    --mud-palette-surface: #1e1e1e;
    --mud-palette-text-primary: rgba(255, 255, 255, 0.87);
    --mud-palette-text-secondary: rgba(255, 255, 255, 0.6);
}

.mud-theme-dark .login-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.mud-theme-dark .login-paper {
    background: rgba(30, 30, 30, 0.95);
    color: var(--mud-palette-text-primary);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.h-100 {
    height: 100%;
}

.w-100 {
    width: 100%;
}

.text-center {
    text-align: center;
}

.cursor-pointer {
    cursor: pointer;
}

.overflow-hidden {
    overflow: hidden;
}

.border-radius-8 {
    border-radius: 8px;
}

.border-radius-12 {
    border-radius: 12px;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.mud-theme-dark .loading-overlay {
    background: rgba(18, 18, 18, 0.8);
}
