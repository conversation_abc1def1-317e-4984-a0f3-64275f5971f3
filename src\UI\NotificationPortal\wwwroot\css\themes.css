/* Professional Theme System for NotificationService Portal */

:root {
    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    
    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    
    /* Spacing Scale */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light Theme */
.mud-theme-light {
    /* Primary Colors */
    --mud-palette-primary: #2563eb;
    --mud-palette-primary-hover: #1d4ed8;
    --mud-palette-primary-text: #ffffff;
    --mud-palette-primary-lighten: #dbeafe;
    --mud-palette-primary-darken: #1e40af;
    
    /* Secondary Colors */
    --mud-palette-secondary: #7c3aed;
    --mud-palette-secondary-hover: #6d28d9;
    --mud-palette-secondary-text: #ffffff;
    --mud-palette-secondary-lighten: #ede9fe;
    --mud-palette-secondary-darken: #5b21b6;
    
    /* Success Colors */
    --mud-palette-success: #059669;
    --mud-palette-success-hover: #047857;
    --mud-palette-success-text: #ffffff;
    --mud-palette-success-lighten: #d1fae5;
    --mud-palette-success-darken: #065f46;
    
    /* Warning Colors */
    --mud-palette-warning: #d97706;
    --mud-palette-warning-hover: #b45309;
    --mud-palette-warning-text: #ffffff;
    --mud-palette-warning-lighten: #fef3c7;
    --mud-palette-warning-darken: #92400e;
    
    /* Error Colors */
    --mud-palette-error: #dc2626;
    --mud-palette-error-hover: #b91c1c;
    --mud-palette-error-text: #ffffff;
    --mud-palette-error-lighten: #fee2e2;
    --mud-palette-error-darken: #991b1b;
    
    /* Info Colors */
    --mud-palette-info: #0891b2;
    --mud-palette-info-hover: #0e7490;
    --mud-palette-info-text: #ffffff;
    --mud-palette-info-lighten: #cffafe;
    --mud-palette-info-darken: #155e75;
    
    /* Background Colors */
    --mud-palette-background: #ffffff;
    --mud-palette-background-grey: #f8fafc;
    --mud-palette-surface: #ffffff;
    --mud-palette-drawer-background: #ffffff;
    --mud-palette-appbar-background: #ffffff;
    
    /* Text Colors */
    --mud-palette-text-primary: #0f172a;
    --mud-palette-text-secondary: #64748b;
    --mud-palette-text-disabled: #cbd5e1;
    
    /* Border Colors */
    --mud-palette-divider: #e2e8f0;
    --mud-palette-divider-light: #f1f5f9;
    --mud-palette-lines-default: #e2e8f0;
    --mud-palette-lines-inputs: #d1d5db;
    
    /* Action Colors */
    --mud-palette-action-default: #64748b;
    --mud-palette-action-disabled: #cbd5e1;
    --mud-palette-action-disabled-background: #f1f5f9;
}

/* Dark Theme */
.mud-theme-dark {
    /* Primary Colors */
    --mud-palette-primary: #3b82f6;
    --mud-palette-primary-hover: #2563eb;
    --mud-palette-primary-text: #ffffff;
    --mud-palette-primary-lighten: #1e3a8a;
    --mud-palette-primary-darken: #1d4ed8;
    
    /* Secondary Colors */
    --mud-palette-secondary: #8b5cf6;
    --mud-palette-secondary-hover: #7c3aed;
    --mud-palette-secondary-text: #ffffff;
    --mud-palette-secondary-lighten: #4c1d95;
    --mud-palette-secondary-darken: #6d28d9;
    
    /* Success Colors */
    --mud-palette-success: #10b981;
    --mud-palette-success-hover: #059669;
    --mud-palette-success-text: #ffffff;
    --mud-palette-success-lighten: #064e3b;
    --mud-palette-success-darken: #047857;
    
    /* Warning Colors */
    --mud-palette-warning: #f59e0b;
    --mud-palette-warning-hover: #d97706;
    --mud-palette-warning-text: #ffffff;
    --mud-palette-warning-lighten: #78350f;
    --mud-palette-warning-darken: #b45309;
    
    /* Error Colors */
    --mud-palette-error: #ef4444;
    --mud-palette-error-hover: #dc2626;
    --mud-palette-error-text: #ffffff;
    --mud-palette-error-lighten: #7f1d1d;
    --mud-palette-error-darken: #b91c1c;
    
    /* Info Colors */
    --mud-palette-info: #06b6d4;
    --mud-palette-info-hover: #0891b2;
    --mud-palette-info-text: #ffffff;
    --mud-palette-info-lighten: #164e63;
    --mud-palette-info-darken: #0e7490;
    
    /* Background Colors */
    --mud-palette-background: #0f172a;
    --mud-palette-background-grey: #1e293b;
    --mud-palette-surface: #1e293b;
    --mud-palette-drawer-background: #1e293b;
    --mud-palette-appbar-background: #1e293b;
    
    /* Text Colors */
    --mud-palette-text-primary: #f8fafc;
    --mud-palette-text-secondary: #cbd5e1;
    --mud-palette-text-disabled: #64748b;
    
    /* Border Colors */
    --mud-palette-divider: #334155;
    --mud-palette-divider-light: #475569;
    --mud-palette-lines-default: #334155;
    --mud-palette-lines-inputs: #475569;
    
    /* Action Colors */
    --mud-palette-action-default: #cbd5e1;
    --mud-palette-action-disabled: #64748b;
    --mud-palette-action-disabled-background: #334155;
}

/* Global Typography */
* {
    font-family: var(--font-family-primary);
}

.mud-typography {
    font-family: var(--font-family-primary);
}

.mud-typography-h1,
.mud-typography-h2,
.mud-typography-h3,
.mud-typography-h4,
.mud-typography-h5,
.mud-typography-h6 {
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.025em;
}

.mud-typography-subtitle1,
.mud-typography-subtitle2 {
    font-weight: var(--font-weight-medium);
}

.mud-typography-body1,
.mud-typography-body2 {
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
}

.mud-typography-caption {
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.025em;
}

/* Code and Monospace */
code,
pre,
.mud-code,
.font-monospace {
    font-family: var(--font-family-mono);
    font-weight: var(--font-weight-medium);
}

/* Enhanced Component Styles */
.mud-paper {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: box-shadow var(--transition-normal);
}

.mud-paper:hover {
    box-shadow: var(--shadow-md);
}

.mud-card {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    border: 1px solid var(--mud-palette-divider-light);
}

.mud-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.mud-button-root {
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    text-transform: none;
}

.mud-button-filled {
    box-shadow: var(--shadow-sm);
}

.mud-button-filled:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.mud-input {
    border-radius: var(--radius-md);
}

.mud-input-outlined .mud-input-outlined-border {
    border-radius: var(--radius-md);
}

.mud-select {
    border-radius: var(--radius-md);
}

.mud-menu {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--mud-palette-divider);
}

.mud-popover {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--mud-palette-divider);
}

.mud-dialog {
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
}

.mud-drawer {
    box-shadow: var(--shadow-lg);
}

.mud-appbar {
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(8px);
    background-color: rgba(255, 255, 255, 0.95);
}

.mud-theme-dark .mud-appbar {
    background-color: rgba(30, 41, 59, 0.95);
}

.mud-table {
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.mud-table-head {
    background-color: var(--mud-palette-background-grey);
}

.mud-table-row:hover {
    background-color: var(--mud-palette-background-grey);
}

/* Custom Utility Classes */
.glass-effect {
    backdrop-filter: blur(12px);
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.mud-theme-dark .glass-effect {
    background-color: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-primary {
    background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-secondary) 100%);
}

.gradient-success {
    background: linear-gradient(135deg, var(--mud-palette-success) 0%, var(--mud-palette-info) 100%);
}

.gradient-warning {
    background: linear-gradient(135deg, var(--mud-palette-warning) 0%, var(--mud-palette-error) 100%);
}

.text-gradient {
    background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .mud-card {
        border-radius: var(--radius-lg);
    }
    
    .mud-dialog {
        border-radius: var(--radius-xl);
        margin: var(--spacing-md);
    }
}

/* Focus States */
.mud-button-root:focus-visible,
.mud-input:focus-within,
.mud-select:focus-within {
    outline: 2px solid var(--mud-palette-primary);
    outline-offset: 2px;
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, 
        var(--mud-palette-background-grey) 25%, 
        var(--mud-palette-divider-light) 50%, 
        var(--mud-palette-background-grey) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Status Indicators */
.status-indicator {
    position: relative;
    display: inline-flex;
    align-items: center;
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--spacing-sm);
}

.status-indicator.status-online::before {
    background-color: var(--mud-palette-success);
    box-shadow: 0 0 0 2px var(--mud-palette-success-lighten);
}

.status-indicator.status-offline::before {
    background-color: var(--mud-palette-error);
    box-shadow: 0 0 0 2px var(--mud-palette-error-lighten);
}

.status-indicator.status-warning::before {
    background-color: var(--mud-palette-warning);
    box-shadow: 0 0 0 2px var(--mud-palette-warning-lighten);
}

/* Professional Scrollbars */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--mud-palette-background-grey);
}

::-webkit-scrollbar-thumb {
    background: var(--mud-palette-divider);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--mud-palette-text-secondary);
}

/* Print Styles */
@media print {
    .mud-appbar,
    .mud-drawer,
    .mud-fab {
        display: none !important;
    }
    
    .mud-main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
}
